# SQL转换器整合方案

## 📋 概述

将原来分散的 `ComplexSqlConverter` 和 `SqlCompatibilityUtil` 整合为统一的 `UnifiedSqlConverter`，通过单一的 `UnifiedSqlInterceptor` 拦截器处理所有SQL转换需求。

## 🏗️ 架构设计

### 原有架构问题
- **重复逻辑**：两个转换器有重复的转换规则
- **调用复杂**：需要判断使用哪个转换器
- **维护困难**：修改需要同步两个地方
- **遗漏风险**：某些SQL可能没有经过完整转换

### 新架构优势
- **统一入口**：所有SQL都通过 `UnifiedSqlInterceptor` 处理
- **分阶段处理**：按照逻辑顺序分4个阶段处理
- **易于维护**：只需要维护一套转换规则
- **完整覆盖**：确保所有SQL都得到正确处理

## 🔄 转换流程

### 第一阶段：预处理和清理
```java
private static String preprocessSql(String sql)
```
- 基础格式清理
- 移除反引号
- 处理LIMIT语法

### 第二阶段：MySQL特有语法转换
```java
private static String convertMysqlSyntax(String sql)
```
- **初始化语句移除**：处理 `(Select @rowindex:=-1) xxx` 模式
- **变量转换**：`@rowindex := @rowindex + 1` → `ROW_NUMBER() OVER(ORDER BY 1)`
- **函数转换**：`NOW()` → `SYSDATE`，`IFNULL()` → `NVL()`
- **其他语法**：`REPLACE INTO` → `INSERT OR REPLACE INTO`

### 第三阶段：变量和函数转换
```java
private static String convertVariablesAndFunctions(String sql)
```
- 扩展点：可添加更多转换逻辑

### 第四阶段：后处理和格式化
```java
private static String postprocessSql(String sql)
```
- **关键字连接修复**：`SELECTROW_NUMBER` → `SELECT ROW_NUMBER`
- **格式清理**：移除多余空格和逗号
- **语法完善**：确保 `ROW_NUMBER()` 有括号

## 🛡️ 安全机制

### 字段名保护
- **精确匹配**：使用完整模式匹配，避免误处理正常字段名
- **长度限制**：别名限制为0-20个字符，避免过度匹配
- **单词边界**：使用 `\b` 确保不会破坏正常SQL

### 错误处理
- **异常捕获**：转换失败时返回原始SQL，不影响业务
- **日志记录**：详细记录转换过程和错误信息
- **向后兼容**：保留原有方法签名，确保平滑迁移

## 📁 文件结构

```
src/main/java/com/goodsogood/ows/dmconfig/
├── UnifiedSqlConverter.java          # 统一SQL转换器
├── UnifiedSqlInterceptor.java        # 统一SQL拦截器
├── MyBatisInterceptorConfig.java     # 配置类（已更新）
├── ComplexSqlConverter.java          # 原文件（可保留作为备份）
└── SqlCompatibilityUtil.java         # 原文件（可保留作为备份）

src/main/java/com/goodsogood/ows/controller/
└── UnifiedSqlTestController.java     # 测试控制器
```

## 🧪 测试接口

### 基础测试
```bash
# 统一转换器测试
GET /test/unified-sql/test-conversion

# 字段名保护测试
GET /test/unified-sql/test-field-protection

# 初始化语句移除测试
GET /test/unified-sql/test-initialization-removal

# MySQL函数转换测试
GET /test/unified-sql/test-mysql-functions

# 综合测试
GET /test/unified-sql/test-comprehensive
```

## 🔧 配置说明

### MyBatis配置
```java
@Configuration
public class MyBatisInterceptorConfig {
    
    @Bean
    public UnifiedSqlInterceptor unifiedSqlInterceptor() {
        return new UnifiedSqlInterceptor();
    }
    
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            configuration.addInterceptor(unifiedSqlInterceptor());
        };
    }
}
```

## 🚀 使用方式

### 自动拦截（推荐）
所有通过MyBatis执行的SQL都会自动被拦截和转换，无需修改业务代码。

### 手动调用
```java
// 统一转换方法
String convertedSql = UnifiedSqlConverter.convertSql(originalSql);

// 向后兼容方法
String convertedSql = UnifiedSqlConverter.smartConvert(originalSql);
String convertedSql = UnifiedSqlConverter.simpleConvert(originalSql);
```

## 📊 转换示例

### 复杂SQL转换
```sql
-- 原始SQL
SELECT @rowindex := @rowindex + 1 AS rn, data 
FROM table grades,(Select @rowindex:=-1) b 
ORDER BY data

-- 转换后
SELECT ROW_NUMBER() OVER(ORDER BY 1) AS rn, data 
FROM table grades 
ORDER BY data
```

### 字段名保护
```sql
-- 原始SQL（保持不变）
SELECT tmp1.orgId, t.fromDate FROM table1 tmp1, table2 t

-- 转换后（字段名完整保留）
SELECT tmp1.orgId, t.fromDate FROM table1 tmp1, table2 t
```

## ⚡ 性能优化

- **早期退出**：空SQL直接返回
- **分阶段处理**：避免重复扫描
- **精确匹配**：减少不必要的正则表达式操作
- **异常处理**：快速失败，不影响主流程

## 🔄 迁移指南

1. **部署新文件**：添加 `UnifiedSqlConverter` 和 `UnifiedSqlInterceptor`
2. **更新配置**：修改 `MyBatisInterceptorConfig` 使用新拦截器
3. **测试验证**：使用测试接口验证转换效果
4. **逐步替换**：将业务代码中的直接调用改为统一转换器
5. **清理旧代码**：确认无问题后可移除旧的转换器

## 📝 注意事项

- **向后兼容**：保留了原有方法签名，确保现有代码不受影响
- **日志监控**：注意观察转换日志，及时发现问题
- **测试覆盖**：建议对关键SQL进行充分测试
- **性能监控**：关注SQL转换对性能的影响

## 🎯 未来扩展

- **规则配置化**：支持通过配置文件定义转换规则
- **缓存机制**：对常用SQL转换结果进行缓存
- **监控面板**：提供转换统计和监控界面
- **插件机制**：支持自定义转换插件
