// 调试具体的grades,(Select @rowindex:=-1) b模式
public class DebugSpecificPattern {
    public static void main(String[] args) {
        String testString = "grades,(Select @rowindex:=-1) b";
        System.out.println("测试字符串: " + testString);
        System.out.println();
        
        // 测试各种可能的正则表达式
        String[] patterns = {
            // 我们添加的新模式
            "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
            
            // 更精确的模式
            "(?i),\\(Select\\s+@rowindex:=-1\\)\\s*b",
            
            // 更通用的模式
            "(?i),\\(\\s*Select\\s+@rowindex\\s*:=\\s*-\\d+\\s*\\)\\s*\\w*",
            
            // 最宽松的模式
            "(?i),\\([^)]*@rowindex[^)]*\\)\\s*\\w*",
            
            // 分步测试
            ",\\(Select @rowindex:=-1\\) b",
            "(?i),\\(Select @rowindex:=-1\\) b"
        };
        
        for (int i = 0; i < patterns.length; i++) {
            String pattern = patterns[i];
            System.out.println("模式 " + (i + 1) + ": " + pattern);
            
            try {
                String result = testString.replaceAll(pattern, "");
                System.out.println("结果: " + result);
                
                if (!result.equals(testString)) {
                    System.out.println("✅ 匹配成功！");
                } else {
                    System.out.println("❌ 不匹配");
                }
            } catch (Exception e) {
                System.out.println("❌ 正则表达式错误: " + e.getMessage());
            }
            
            System.out.println();
        }
        
        // 手动分析字符串
        System.out.println("=== 手动分析 ===");
        System.out.println("字符串: " + testString);
        System.out.println("长度: " + testString.length());
        
        for (int i = 0; i < testString.length(); i++) {
            char c = testString.charAt(i);
            System.out.println("位置 " + i + ": '" + c + "' (ASCII: " + (int)c + ")");
        }
        
        // 测试简单的匹配
        System.out.println("\n=== 简单匹配测试 ===");
        boolean contains1 = testString.contains(",(Select");
        boolean contains2 = testString.contains("@rowindex:=-1");
        boolean contains3 = testString.contains(") b");
        
        System.out.println("包含 ',(Select': " + contains1);
        System.out.println("包含 '@rowindex:=-1': " + contains2);
        System.out.println("包含 ') b': " + contains3);
        
        if (contains1 && contains2 && contains3) {
            System.out.println("✅ 所有组件都存在，应该能匹配");
        }
        
        // 测试最简单的替换
        System.out.println("\n=== 最简单的替换测试 ===");
        String simple = testString.replace(",(Select @rowindex:=-1) b", "");
        System.out.println("简单替换结果: '" + simple + "'");
        
        if (!simple.equals(testString)) {
            System.out.println("✅ 简单替换成功");
            System.out.println("建议使用精确匹配而不是复杂正则表达式");
        }
    }
}
