package com.goodsogood.ows.controller;

import com.goodsogood.ows.dmconfig.ComplexSqlConverter;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 全面的SQL转换测试控制器
 * 测试所有可能的MySQL到达梦的SQL转换情况
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/test/comprehensive-sql")
@Log4j2
public class ComprehensiveSqlTestController {

    /**
     * 测试MySQL函数转换
     */
    @GetMapping("/test-mysql-functions")
    public String testMysqlFunctions() {
        log.info("🧪 测试MySQL函数转换...");
        
        String[] testSqls = {
            "SELECT IFNULL(name, 'Unknown') FROM users",
            "SELECT UNIX_TIMESTAMP(created_at) FROM orders",
            "SELECT DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS formatted_date",
            "SELECT CONCAT_WS('-', first_name, last_name) AS full_name FROM users",
            "SELECT GROUP_CONCAT(name) FROM categories",
            "SELECT SUBSTRING_INDEX(email, '@', 1) AS username FROM users",
            "SELECT FIND_IN_SET('admin', roles) FROM user_roles"
        };
        
        return testSqlConversions("MySQL函数", testSqls);
    }

    /**
     * 测试特殊语法转换
     */
    @GetMapping("/test-special-syntax")
    public String testSpecialSyntax() {
        log.info("🧪 测试特殊语法转换...");
        
        String[] testSqls = {
            "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>') ON DUPLICATE KEY UPDATE name = VALUES(name)",
            "REPLACE INTO cache (key, value) VALUES ('config', 'data')",
            "SELECT * FROM table1 STRAIGHT_JOIN table2 ON table1.id = table2.id",
            "SELECT * FROM users FORCE INDEX (idx_name) WHERE name = 'John'",
            "SELECT SQL_CALC_FOUND_ROWS * FROM users LIMIT 10",
            "SELECT FOUND_ROWS() AS total_count",
            "SELECT LAST_INSERT_ID() AS new_id",
            "SELECT CONNECTION_ID() AS session_id"
        };
        
        return testSqlConversions("特殊语法", testSqls);
    }

    /**
     * 测试数据类型转换
     */
    @GetMapping("/test-data-types")
    public String testDataTypes() {
        log.info("🧪 测试数据类型转换...");
        
        String[] testSqls = {
            "SELECT CAST(price AS SIGNED) FROM products",
            "SELECT CAST(quantity AS UNSIGNED) FROM inventory",
            "SELECT CAST(rate AS DECIMAL(10,2)) FROM rates",
            "SELECT CAST(name AS CHAR(50)) FROM users",
            "SELECT CAST(created_at AS DATE) FROM orders",
            "SELECT CAST(updated_at AS DATETIME) FROM logs"
        };
        
        return testSqlConversions("数据类型", testSqls);
    }

    /**
     * 测试JSON函数转换
     */
    @GetMapping("/test-json-functions")
    public String testJsonFunctions() {
        log.info("🧪 测试JSON函数转换...");
        
        String[] testSqls = {
            "SELECT JSON_EXTRACT(data, '$.name') FROM json_table",
            "SELECT JSON_UNQUOTE(JSON_EXTRACT(data, '$.email')) FROM json_table",
            "SELECT JSON_OBJECT('name', name, 'age', age) FROM users",
            "SELECT JSON_ARRAY(id, name, email) FROM users"
        };
        
        return testSqlConversions("JSON函数", testSqls);
    }

    /**
     * 测试正则表达式函数
     */
    @GetMapping("/test-regex-functions")
    public String testRegexFunctions() {
        log.info("🧪 测试正则表达式函数转换...");
        
        String[] testSqls = {
            "SELECT * FROM users WHERE email REGEXP '^[a-zA-Z0-9]+@[a-zA-Z0-9]+\\.[a-zA-Z]{2,}$'",
            "SELECT * FROM products WHERE name RLIKE '[0-9]+'",
            "SELECT REGEXP_REPLACE(phone, '[^0-9]', '') AS clean_phone FROM contacts"
        };
        
        return testSqlConversions("正则表达式", testSqls);
    }

    /**
     * 测试数学函数转换
     */
    @GetMapping("/test-math-functions")
    public String testMathFunctions() {
        log.info("🧪 测试数学函数转换...");
        
        String[] testSqls = {
            "SELECT POW(base, exponent) FROM calculations",
            "SELECT LOG(value) FROM measurements",
            "SELECT LOG10(population) FROM cities",
            "SELECT RAND() AS random_value"
        };
        
        return testSqlConversions("数学函数", testSqls);
    }

    /**
     * 测试复杂嵌套查询
     */
    @GetMapping("/test-complex-nested")
    public String testComplexNested() {
        log.info("🧪 测试复杂嵌套查询转换...");
        
        String[] testSqls = {
            "SELECT * FROM (SELECT @rownum := @rownum + 1 AS row_number, t.* FROM (SELECT * FROM users ORDER BY created_at) t, (SELECT @rownum := 0) r) ranked WHERE row_number <= 10",
            "SELECT IFNULL(AVG(CAST(score AS SIGNED)), 0) FROM (SELECT SUBSTRING_INDEX(data, ',', 1) AS score FROM test_results WHERE JSON_EXTRACT(metadata, '$.valid') = 1) scores",
            "SELECT DATE_FORMAT(created_at, '%Y-%m') AS month, COUNT(*) FROM orders WHERE created_at REGEXP '^[0-9]{4}-[0-9]{2}' GROUP BY month"
        };
        
        return testSqlConversions("复杂嵌套查询", testSqls);
    }

    /**
     * 测试所有转换
     */
    @GetMapping("/test-all")
    public String testAllConversions() {
        log.info("🧪 测试所有SQL转换...");
        
        StringBuilder result = new StringBuilder();
        result.append("=== 全面SQL转换测试 ===\n\n");
        
        result.append(testMysqlFunctions()).append("\n\n");
        result.append(testSpecialSyntax()).append("\n\n");
        result.append(testDataTypes()).append("\n\n");
        result.append(testJsonFunctions()).append("\n\n");
        result.append(testRegexFunctions()).append("\n\n");
        result.append(testMathFunctions()).append("\n\n");
        result.append(testComplexNested()).append("\n\n");
        
        return result.toString();
    }

    /**
     * 通用SQL转换测试方法
     */
    private String testSqlConversions(String category, String[] testSqls) {
        StringBuilder result = new StringBuilder();
        result.append("=== ").append(category).append("转换测试 ===\n\n");
        
        for (int i = 0; i < testSqls.length; i++) {
            String originalSql = testSqls[i];
            result.append("测试 ").append(i + 1).append(":\n");
            result.append("原始: ").append(originalSql).append("\n");
            
            try {
                String convertedSql = ComplexSqlConverter.smartConvert(originalSql);
                result.append("转换: ").append(convertedSql).append("\n");
                result.append("状态: ✅ 成功\n");
            } catch (Exception e) {
                result.append("转换: 失败 - ").append(e.getMessage()).append("\n");
                result.append("状态: ❌ 失败\n");
            }
            
            result.append("\n");
        }
        
        return result.toString();
    }

    /**
     * 自定义SQL测试
     */
    @GetMapping("/test-custom")
    public String testCustomSql(@RequestParam String sql) {
        log.info("🧪 测试自定义SQL转换: {}", sql);
        
        try {
            String convertedSql = ComplexSqlConverter.smartConvert(sql);
            
            StringBuilder result = new StringBuilder();
            result.append("自定义SQL转换测试:\n\n");
            result.append("原始SQL: ").append(sql).append("\n\n");
            result.append("转换结果: ").append(convertedSql).append("\n\n");
            result.append("是否复杂SQL: ").append(ComplexSqlConverter.isComplexSql(sql)).append("\n");
            result.append("转换状态: ✅ 成功\n");
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("❌ 自定义SQL转换失败: {}", e.getMessage(), e);
            return "自定义SQL转换失败: " + e.getMessage();
        }
    }
}
