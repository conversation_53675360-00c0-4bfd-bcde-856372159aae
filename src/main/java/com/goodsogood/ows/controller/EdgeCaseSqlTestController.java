package com.goodsogood.ows.controller;

import com.goodsogood.ows.dmconfig.ComplexSqlConverter;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 边界情况SQL测试控制器
 * 测试各种复杂的空格、换行、注释等边界情况
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/test/edge-cases")
@Log4j2
public class EdgeCaseSqlTestController {

    /**
     * 测试空格变体
     */
    @GetMapping("/test-whitespace-variants")
    public String testWhitespaceVariants() {
        log.info("🧪 测试空格变体...");
        
        String[] testCases = {
            // 各种空格组合
            "SELECT @rowindex:=@rowindex+1 AS rowindex FROM table",
            "SELECT @rowindex := @rowindex + 1 AS rowindex FROM table",
            "SELECT @rowindex  :=  @rowindex  +  1  AS  rowindex FROM table",
            "SELECT @rowindex : = @rowindex + 1 AS rowindex FROM table",
            "SELECT @rowindex:= @rowindex+ 1 AS rowindex FROM table",
            
            // 制表符和换行
            "SELECT\t@rowindex\t:=\t@rowindex\t+\t1\tAS\trowindex\nFROM\ttable",
            "SELECT\n@rowindex\n:=\n@rowindex\n+\n1\nAS\nrowindex\nFROM\ntable",
            
            // 混合空白字符
            "SELECT \t@rowindex \n := \t@rowindex \n + \t1 \nAS \trowindex FROM table",
            
            // 极端情况
            "SELECT@rowindex:=@rowindex+1AS rowindex FROM table",
            "SELECT    @rowindex    :=    @rowindex    +    1    AS    rowindex    FROM    table"
        };
        
        return testSqlConversions("空格变体", testCases);
    }

    /**
     * 测试注释情况
     */
    @GetMapping("/test-comments")
    public String testComments() {
        log.info("🧪 测试注释情况...");
        
        String[] testCases = {
            // 单行注释
            "SELECT @rowindex := @rowindex + 1 AS rowindex -- 行号变量\nFROM table",
            "SELECT @rowindex := @rowindex + 1 AS rowindex # 行号变量\nFROM table",
            
            // 多行注释
            "SELECT @rowindex /* 变量 */ := @rowindex /* 递增 */ + 1 AS rowindex FROM table",
            "SELECT /* 开始 */ @rowindex := @rowindex + 1 /* 结束 */ AS rowindex FROM table",
            
            // 复杂注释
            "SELECT @rowindex /* 这是一个\n多行注释 */ := @rowindex + 1 AS rowindex FROM table",
            
            // 注释中包含特殊字符
            "SELECT @rowindex /* 注释中有@符号 */ := @rowindex + 1 AS rowindex FROM table",
            
            // 嵌套注释风格（虽然MySQL不支持真正的嵌套）
            "SELECT @rowindex /* 外层 /* 内层 */ 注释 */ := @rowindex + 1 AS rowindex FROM table"
        };
        
        return testSqlConversions("注释情况", testCases);
    }

    /**
     * 测试字符串字面量
     */
    @GetMapping("/test-string-literals")
    public String testStringLiterals() {
        log.info("🧪 测试字符串字面量...");
        
        String[] testCases = {
            // 字符串中包含@符号
            "SELECT @rowindex := @rowindex + 1 AS rowindex, '<EMAIL>' AS email FROM table",
            "SELECT @rowindex := @rowindex + 1 AS rowindex, \"<EMAIL>\" AS email FROM table",
            
            // 字符串中包含SQL关键字
            "SELECT @rowindex := @rowindex + 1 AS rowindex, 'SELECT * FROM users' AS query FROM table",
            
            // 转义字符
            "SELECT @rowindex := @rowindex + 1 AS rowindex, 'It\\'s a test' AS text FROM table",
            "SELECT @rowindex := @rowindex + 1 AS rowindex, \"He said \\\"Hello\\\"\" AS text FROM table",
            
            // 复杂字符串
            "SELECT @rowindex := @rowindex + 1 AS rowindex, 'Line1\\nLine2\\tTab' AS multiline FROM table"
        };
        
        return testSqlConversions("字符串字面量", testCases);
    }

    /**
     * 测试嵌套和复杂结构
     */
    @GetMapping("/test-nested-complex")
    public String testNestedComplex() {
        log.info("🧪 测试嵌套复杂结构...");
        
        String[] testCases = {
            // 深度嵌套
            "SELECT * FROM (SELECT @rowindex := @rowindex + 1 AS rn, * FROM (SELECT * FROM users) t, (SELECT @rowindex := 0) r) ranked",
            
            // 多个变量
            "SELECT @row := @row + 1 AS row_num, @total := @total + amount AS running_total FROM sales, (SELECT @row := 0, @total := 0) vars",
            
            // 复杂的ORDER BY
            "SELECT @rowindex := @rowindex + 1 AS rowindex, data FROM (SELECT * FROM table ORDER BY CASE WHEN status = 'active' THEN 1 ELSE 2 END, created_at DESC) t, (SELECT @rowindex := 0) r",
            
            // 多表JOIN
            "SELECT @rowindex := @rowindex + 1 AS rowindex, u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id, (SELECT @rowindex := 0) r ORDER BY u.created_at",
            
            // 子查询中的变量
            "SELECT * FROM table WHERE id IN (SELECT id FROM (SELECT @rowindex := @rowindex + 1 AS rn, id FROM items, (SELECT @rowindex := 0) r) ranked WHERE rn <= 10)"
        };
        
        return testSqlConversions("嵌套复杂结构", testCases);
    }

    /**
     * 测试错误和边界情况
     */
    @GetMapping("/test-error-cases")
    public String testErrorCases() {
        log.info("🧪 测试错误和边界情况...");
        
        String[] testCases = {
            // 语法错误但需要处理
            "SELECT @rowindex := @rowindex + AS rowindex FROM table", // 缺少数字
            "SELECT @rowindex := @rowindex + 1 rowindex FROM table", // 缺少AS
            "SELECT @rowindex @rowindex + 1 AS rowindex FROM table", // 缺少:=
            
            // 不完整的SQL
            "SELECT @rowindex := @rowindex + 1",
            "@rowindex := @rowindex + 1 AS rowindex",
            
            // 特殊字符
            "SELECT @rowindex := @rowindex + 1 AS `rowindex` FROM `table`",
            "SELECT @rowindex := @rowindex + 1 AS [rowindex] FROM [table]",
            
            // Unicode和特殊字符
            "SELECT @rowindex := @rowindex + 1 AS 行号 FROM 表格",
            
            // 极长的SQL
            "SELECT @rowindex := @rowindex + 1 AS rowindex, " + "col1, ".repeat(100) + "col101 FROM very_long_table_name_that_exceeds_normal_limits"
        };
        
        return testSqlConversions("错误和边界情况", testCases);
    }

    /**
     * 测试所有边界情况
     */
    @GetMapping("/test-all-edge-cases")
    public String testAllEdgeCases() {
        log.info("🧪 测试所有边界情况...");
        
        StringBuilder result = new StringBuilder();
        result.append("=== 全面边界情况测试 ===\n\n");
        
        result.append(testWhitespaceVariants()).append("\n\n");
        result.append(testComments()).append("\n\n");
        result.append(testStringLiterals()).append("\n\n");
        result.append(testNestedComplex()).append("\n\n");
        result.append(testErrorCases()).append("\n\n");
        
        return result.toString();
    }

    /**
     * 测试性能压力情况
     */
    @GetMapping("/test-performance")
    public String testPerformance() {
        log.info("🧪 测试性能压力情况...");
        
        // 生成大量测试用例
        String[] largeSqlCases = new String[100];
        for (int i = 0; i < 100; i++) {
            largeSqlCases[i] = String.format(
                "SELECT @rowindex := @rowindex + 1 AS rowindex, id, name, email, created_at FROM users_%d, (SELECT @rowindex := 0) r ORDER BY created_at LIMIT %d",
                i, i * 10
            );
        }
        
        long startTime = System.currentTimeMillis();
        String result = testSqlConversions("性能压力测试", largeSqlCases);
        long endTime = System.currentTimeMillis();
        
        return String.format("性能测试完成，耗时: %dms\n\n%s", endTime - startTime, result);
    }

    /**
     * 通用SQL转换测试方法
     */
    private String testSqlConversions(String category, String[] testSqls) {
        StringBuilder result = new StringBuilder();
        result.append("=== ").append(category).append("测试 ===\n\n");
        
        int successCount = 0;
        int totalCount = testSqls.length;
        
        for (int i = 0; i < testSqls.length; i++) {
            String originalSql = testSqls[i];
            result.append("测试 ").append(i + 1).append(":\n");
            result.append("原始: ").append(originalSql.replaceAll("\\s+", " ")).append("\n");
            
            try {
                long startTime = System.currentTimeMillis();
                String convertedSql = ComplexSqlConverter.smartConvert(originalSql);
                long endTime = System.currentTimeMillis();
                
                result.append("转换: ").append(convertedSql.replaceAll("\\s+", " ")).append("\n");
                result.append("耗时: ").append(endTime - startTime).append("ms\n");
                result.append("包含@: ").append(convertedSql.contains("@") ? "❌" : "✅").append("\n");
                result.append("状态: ✅ 成功\n");
                successCount++;
            } catch (Exception e) {
                result.append("转换: 失败 - ").append(e.getMessage()).append("\n");
                result.append("状态: ❌ 失败\n");
            }
            
            result.append("\n");
        }
        
        result.append("总结: ").append(successCount).append("/").append(totalCount)
              .append(" 成功，成功率: ").append(String.format("%.1f%%", (double)successCount/totalCount*100)).append("\n");
        
        return result.toString();
    }
}
