package com.goodsogood.ows.controller;

import com.goodsogood.ows.dmconfig.SqlConversionMonitor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SQL转换统计控制器
 * 提供SQL转换的监控和统计信息
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/monitor/sql-conversion")
@Log4j2
public class SqlConversionStatsController {

    @Autowired
    private SqlConversionMonitor monitor;

    /**
     * 获取转换统计信息
     */
    @GetMapping("/stats")
    public SqlConversionMonitor.ConversionStats getStats() {
        log.info("获取SQL转换统计信息");
        return monitor.getStats();
    }

    /**
     * 获取格式化的统计报告
     */
    @GetMapping("/report")
    public String getStatsReport() {
        log.info("生成SQL转换统计报告");
        
        SqlConversionMonitor.ConversionStats stats = monitor.getStats();
        
        StringBuilder report = new StringBuilder();
        report.append("=== SQL转换统计报告 ===\n\n");
        
        // 基础统计
        report.append("📊 基础统计:\n");
        report.append("总转换次数: ").append(stats.totalConversions).append("\n");
        report.append("成功转换: ").append(stats.successfulConversions).append("\n");
        report.append("失败转换: ").append(stats.failedConversions).append("\n");
        report.append("成功率: ").append(String.format("%.2f%%", stats.successRate)).append("\n");
        report.append("平均转换时间: ").append(String.format("%.2fms", stats.averageConversionTime)).append("\n\n");
        
        // 转换类型统计
        if (stats.conversionTypeStats != null && !stats.conversionTypeStats.isEmpty()) {
            report.append("🔄 转换类型统计:\n");
            stats.conversionTypeStats.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()))
                .forEach(entry -> report.append("  ")
                    .append(entry.getKey())
                    .append(": ")
                    .append(entry.getValue())
                    .append(" 次\n"));
            report.append("\n");
        }
        
        // 错误统计
        if (stats.errorStats != null && !stats.errorStats.isEmpty()) {
            report.append("❌ 错误统计:\n");
            stats.errorStats.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()))
                .forEach(entry -> report.append("  ")
                    .append(entry.getKey())
                    .append(": ")
                    .append(entry.getValue())
                    .append(" 次\n"));
            report.append("\n");
        }
        
        // 性能统计
        if (stats.performanceStats != null && !stats.performanceStats.isEmpty()) {
            report.append("⚡ 性能统计:\n");
            stats.performanceStats.forEach((metric, value) -> 
                report.append("  ")
                    .append(metric)
                    .append(": ")
                    .append(value)
                    .append("ms\n"));
            report.append("\n");
        }
        
        // 健康状态评估
        report.append("🏥 健康状态评估:\n");
        if (stats.totalConversions == 0) {
            report.append("  状态: 🟡 无转换记录\n");
        } else if (stats.successRate >= 95) {
            report.append("  状态: 🟢 优秀 (成功率 >= 95%)\n");
        } else if (stats.successRate >= 90) {
            report.append("  状态: 🟡 良好 (成功率 >= 90%)\n");
        } else {
            report.append("  状态: 🔴 需要关注 (成功率 < 90%)\n");
        }
        
        if (stats.averageConversionTime <= 10) {
            report.append("  性能: 🟢 优秀 (平均耗时 <= 10ms)\n");
        } else if (stats.averageConversionTime <= 50) {
            report.append("  性能: 🟡 良好 (平均耗时 <= 50ms)\n");
        } else {
            report.append("  性能: 🔴 需要优化 (平均耗时 > 50ms)\n");
        }
        
        report.append("\n========================");
        
        return report.toString();
    }

    /**
     * 重置统计信息
     */
    @PostMapping("/reset")
    public String resetStats() {
        log.info("重置SQL转换统计信息");
        monitor.resetStats();
        return "✅ SQL转换统计信息已重置";
    }

    /**
     * 打印统计报告到日志
     */
    @PostMapping("/print-report")
    public String printReport() {
        log.info("打印SQL转换统计报告到日志");
        monitor.printStatsReport();
        return "✅ 统计报告已打印到日志";
    }

    /**
     * 检查性能警告
     */
    @GetMapping("/check-warnings")
    public String checkWarnings() {
        log.info("检查SQL转换性能警告");
        monitor.checkPerformanceWarnings();
        return "✅ 性能警告检查完成，详情请查看日志";
    }

    /**
     * 获取实时状态
     */
    @GetMapping("/status")
    public String getStatus() {
        SqlConversionMonitor.ConversionStats stats = monitor.getStats();
        
        StringBuilder status = new StringBuilder();
        status.append("🚀 SQL转换器实时状态\n\n");
        status.append("运行状态: ✅ 正常运行\n");
        status.append("总处理量: ").append(stats.totalConversions).append(" 次\n");
        status.append("当前成功率: ").append(String.format("%.1f%%", stats.successRate)).append("\n");
        status.append("平均响应时间: ").append(String.format("%.1fms", stats.averageConversionTime)).append("\n");
        
        // 最近状态
        if (stats.totalConversions > 0) {
            if (stats.successRate >= 95) {
                status.append("服务质量: 🟢 优秀\n");
            } else if (stats.successRate >= 90) {
                status.append("服务质量: 🟡 良好\n");
            } else {
                status.append("服务质量: 🔴 需要关注\n");
            }
        } else {
            status.append("服务质量: ⚪ 暂无数据\n");
        }
        
        status.append("\n💡 提示: 访问 /monitor/sql-conversion/report 查看详细报告");
        
        return status.toString();
    }

    /**
     * 获取简化的JSON格式统计
     */
    @GetMapping("/stats/simple")
    public String getSimpleStats() {
        SqlConversionMonitor.ConversionStats stats = monitor.getStats();
        
        return String.format(
            "{\"total\":%d,\"success\":%d,\"failed\":%d,\"successRate\":%.2f,\"avgTime\":%.2f}",
            stats.totalConversions,
            stats.successfulConversions, 
            stats.failedConversions,
            stats.successRate,
            stats.averageConversionTime
        );
    }
}
