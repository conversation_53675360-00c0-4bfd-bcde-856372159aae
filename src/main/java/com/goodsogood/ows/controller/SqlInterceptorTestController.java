package com.goodsogood.ows.controller;

import com.goodsogood.ows.dmconfig.ComplexSqlConverter;
import com.goodsogood.ows.dmconfig.SqlCompatibilityUtil;
import com.goodsogood.ows.mapper.eval.MetricMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * SQL拦截器测试控制器
 * 用于测试达梦数据库SQL转换拦截器是否正常工作
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/test/sql-interceptor")
@Log4j2
public class SqlInterceptorTestController {

    @Autowired
    private MetricMapper metricMapper;

    /**
     * 测试SQL拦截器
     */
    @GetMapping("/test")
    public String testSqlInterceptor() {
        log.info("🧪 开始测试SQL拦截器...");
        
        try {
            // 调用一个简单的查询来触发拦截器
            Object result = metricMapper.selectByPrimaryKey(1L);
            
            log.info("✅ SQL拦截器测试成功，查询结果: {}", result);
            return "SQL拦截器测试成功！查询已执行，请查看日志以确认拦截器是否工作。";
            
        } catch (Exception e) {
            log.error("❌ SQL拦截器测试失败: {}", e.getMessage(), e);
            return "SQL拦截器测试失败: " + e.getMessage();
        }
    }

    /**
     * 获取拦截器状态
     */
    @GetMapping("/status")
    public String getInterceptorStatus() {
        return "SQL拦截器状态检查接口已就绪。请调用 /test 接口进行实际测试。";
    }

    /**
     * 测试复杂SQL转换
     */
    @GetMapping("/test-complex-sql")
    public String testComplexSqlConversion(@RequestParam(required = false) String sql) {
        log.info("🧪 开始测试复杂SQL转换...");

        // 如果没有提供SQL，使用默认的复杂SQL
        if (sql == null || sql.trim().isEmpty()) {
            sql = "select L.orgId,score1 partyIndex, score2 businessIndex, score3 InnovationIndex from (select tmp1.org_id orgId, round(NVL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, round(NVL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, round(NVL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3  from (select t1.organization_id org_id,NVL(t2.score1,0) score1,NVL(t2.score2,0) score2,NVL(t2.score3,0) score3 from ( select organization_id  from t_organization  where  organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1 ) t1 LEFT JOIN ( select tt1.score_org_id, sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3  from t_score_org_type_count tt1 INNER JOIN t_organization tt2 on tt1.score_org_id = tt2.organization_id  and tt1.org_id =3 and tt1.score_org_type =1 and tt1.parent_score_type in (1,2,3) and tt2.org_type_child in (10280304,10280309,10280314,10280315,10280319) and tt2.organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and tt2.status=1 GROUP BY tt1.score_org_id ) t2 on t1.organization_id = t2.score_org_id ) tmp1,( select scoreMedian1,scoreMedian2,scoreMedian3 from (  SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1   FROM (select score1 from (  select sum(NVL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and    org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  ) tmmp where score1 !=0  union all  select score1 from (  select DISTINCT sum(NVL(tt1.total,0)) score1 from (select organization_id from t_organiza\n" +
                    "tion where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  ) tmmp2 where score1 =0  ORDER BY score1) grades,(Select @rowindex:=-1) b  ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  ) t1,(  SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2   FROM (select score2 from (  select sum(NVL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id  ) tmmp where score2 !=0  union all  select score2 from (  select DISTINCT sum(NVL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id  ) tmmp2 where score2 =0  ORDER BY score2) grades,(Select @rowindex:=-1) b  ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  ) t2,(  SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3   FROM (select score3 from (  select sum(NVL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and    org_type_child in (10280\n" +
                    ",10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id  ) tmmp where score3 !=0  union all  select score3 from (  select DISTINCT sum(NVL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id  ) tmmp2 where score3 =0  ORDER BY score3) grades,(Select @rowindex:=-1) b  ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  ) t3) tmp2 ORDER BY org_id  )as L WHERE 1=1   and orgId IN  ( 4546,4549  )";
        }

        try {
            log.info("原始SQL: {}", sql);

            // 测试基础转换
            String basicConverted = SqlCompatibilityUtil.convertSql(sql);
            log.info("基础转换: {}", basicConverted);

            // 测试复杂转换
            String complexConverted = ComplexSqlConverter.convertComplexSql(sql);
            log.info("复杂转换: {}", complexConverted);

            // 测试智能转换
            String smartConverted = ComplexSqlConverter.smartConvert(sql);
            log.info("智能转换: {}", smartConverted);

            // 检查转换条件
            boolean needsConversion = SqlCompatibilityUtil.needsConversion(sql);
            boolean isComplex = ComplexSqlConverter.isComplexSql(sql);

            StringBuilder result = new StringBuilder();
            result.append("复杂SQL转换测试完成！\n\n");
            result.append("原始SQL: ").append(sql).append("\n\n");
            result.append("基础转换: ").append(basicConverted).append("\n\n");
            result.append("复杂转换: ").append(complexConverted).append("\n\n");
            result.append("智能转换: ").append(smartConverted).append("\n\n");
            result.append("需要转换: ").append(needsConversion).append("\n");
            result.append("是复杂SQL: ").append(isComplex).append("\n");

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 复杂SQL转换测试失败: {}", e.getMessage(), e);
            return "复杂SQL转换测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试空格变体的@rowindex转换
     */
    @GetMapping("/test-rowindex-spaces")
    public String testRowIndexWithSpaces() {
        log.info("🧪 测试@rowindex空格变体转换...");

        // 测试包含空格的@rowindex变体
        String sqlWithSpaces = "select scoreMedian1 from(" +
                "SELECT AVG(g.score1) scoreMedian1 " +
                "FROM(SELECT @rowindex : = @rowindex + 1 AS rowindex, grades.score1 AS score1 " +
                "FROM(select score1 from table1) grades, " +
                "(SELECT @rowindex : = 0) r " +
                "ORDER BY grades.score1) g)";

        try {
            log.info("原始SQL(含空格): {}", sqlWithSpaces);

            // 使用智能转换器
            String smartConverted = ComplexSqlConverter.smartConvert(sqlWithSpaces);
            log.info("智能转换结果: {}", smartConverted);

            StringBuilder result = new StringBuilder();
            result.append("@rowindex空格变体测试完成！\n\n");
            result.append("原始SQL: ").append(sqlWithSpaces).append("\n\n");
            result.append("转换结果: ").append(smartConverted).append("\n\n");
            result.append("包含@符号: ").append(smartConverted.contains("@")).append("\n");
            result.append("转换成功: ").append(!smartConverted.contains("@rowindex")).append("\n");

            return result.toString();

        } catch (Exception e) {
            log.error("❌ @rowindex空格变体测试失败: {}", e.getMessage(), e);
            return "@rowindex空格变体测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试SQL语法修复
     */
    @GetMapping("/test-syntax-fix")
    public String testSyntaxFix() {
        log.info("🧪 测试SQL语法修复...");

        String testSql = "SELECT @rowindex := @rowindex + 1 AS rowindex, name FROM users, (SELECT @rowindex := 0) r ORDER BY name";

        try {
            log.info("原始SQL: {}", testSql);

            String converted = ComplexSqlConverter.smartConvert(testSql);
            log.info("转换结果: {}", converted);

            StringBuilder result = new StringBuilder();
            result.append("SQL语法修复测试:\n\n");
            result.append("原始SQL: ").append(testSql).append("\n\n");
            result.append("转换结果: ").append(converted).append("\n\n");

            // 语法验证
            result.append("语法检查:\n");
            result.append("- 包含ROW_NUMBER(): ").append(converted.contains("ROW_NUMBER()")).append("\n");
            result.append("- 包含OVER(): ").append(converted.contains("OVER(")).append("\n");
            result.append("- 包含ORDER BY: ").append(converted.contains("ORDER BY")).append("\n");
            result.append("- 不包含@符号: ").append(!converted.contains("@")).append("\n");

            // 检查ROW_NUMBER语法是否正确
            boolean validSyntax = converted.matches(".*ROW_NUMBER\\(\\)\\s+OVER\\s*\\([^)]+\\).*");
            result.append("- ROW_NUMBER语法正确: ").append(validSyntax).append("\n\n");

            if (validSyntax && !converted.contains("@")) {
                result.append("状态: ✅ SQL语法修复成功！\n");
            } else {
                result.append("状态: ❌ SQL语法仍有问题\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ SQL语法修复测试失败: {}", e.getMessage(), e);
            return "SQL语法修复测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试详细的SQL转换步骤
     */
    @GetMapping("/test-detailed-conversion")
    public String testDetailedConversion() {
        log.info("🧪 测试详细的SQL转换步骤...");

        String testSql = "SELECT @rowindex := @rowindex + 1 AS rowindex, name FROM users, (SELECT @rowindex := 0) r ORDER BY name";

        try {
            StringBuilder result = new StringBuilder();
            result.append("详细SQL转换步骤测试:\n\n");
            result.append("原始SQL: ").append(testSql).append("\n\n");

            // 步骤1: 移除变量初始化
            String step1 = testSql.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*0\\s*\\)\\s*\\w*", "");
            result.append("步骤1 - 移除初始化: ").append(step1).append("\n\n");

            // 步骤2: 转换@rowindex
            String step2 = step1.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
                                          "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
            result.append("步骤2 - 转换@rowindex: ").append(step2).append("\n\n");

            // 步骤3: 确保关键字空格
            String step3 = step2;
            step3 = step3.replaceAll("(?i)SELECT(?=\\w)", "SELECT ");
            step3 = step3.replaceAll("(?i)FROM(?=\\w)", "FROM ");
            step3 = step3.replaceAll("(?i)ROW_NUMBER(?=\\w)", "ROW_NUMBER ");
            step3 = step3.replaceAll("(?i)OVER(?=\\w)", "OVER ");
            result.append("步骤3 - 确保关键字空格: ").append(step3).append("\n\n");

            // 步骤4: 清理空格
            String step4 = step3.replaceAll("\\s+", " ").trim();
            result.append("步骤4 - 清理空格: ").append(step4).append("\n\n");

            // 使用智能转换器
            String smartConverted = ComplexSqlConverter.smartConvert(testSql);
            result.append("智能转换器结果: ").append(smartConverted).append("\n\n");

            // 语法验证
            result.append("语法检查:\n");
            result.append("- 手动转换包含ROW_NUMBER(): ").append(step4.contains("ROW_NUMBER()")).append("\n");
            result.append("- 手动转换包含OVER(: ").append(step4.contains("OVER(")).append("\n");
            result.append("- 智能转换包含ROW_NUMBER(): ").append(smartConverted.contains("ROW_NUMBER()")).append("\n");
            result.append("- 智能转换包含OVER(: ").append(smartConverted.contains("OVER(")).append("\n");
            result.append("- 手动转换不包含@: ").append(!step4.contains("@")).append("\n");
            result.append("- 智能转换不包含@: ").append(!smartConverted.contains("@")).append("\n");

            // 检查是否有SELECTROW_NUMBER问题
            result.append("- 手动转换无SELECTROW_NUMBER: ").append(!step4.contains("SELECTROW_NUMBER")).append("\n");
            result.append("- 智能转换无SELECTROW_NUMBER: ").append(!smartConverted.contains("SELECTROW_NUMBER")).append("\n");

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 详细转换测试失败: {}", e.getMessage(), e);
            return "详细转换测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试问题SQL模式
     */
    @GetMapping("/test-problematic-patterns")
    public String testProblematicPatterns() {
        log.info("🧪 测试问题SQL模式...");

        String[] problematicSqls = {
            // 1. 基本模式
            "SELECT @rowindex := @rowindex + 1 AS rowindex FROM table, (SELECT @rowindex := 0) r",

            // 2. 复杂嵌套
            "SELECT * FROM (SELECT @rowindex := @rowindex + 1 AS rn, data FROM table, (SELECT @rowindex := 0) r) ranked",

            // 3. 多个初始化
            "SELECT @row := @row + 1 AS rn FROM table, (SELECT @row := 0, @total := 0) vars",

            // 4. 您的复杂SQL
            "select scoreMedian1 from(SELECT AVG(g.score1) scoreMedian1 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM(select score1 from table1) grades, (SELECT @rowindex := 0) r ORDER BY grades.score1) g)",

            // 5. 边界情况
            "SELECT data FROM (SELECT @rowindex := 0) init, (SELECT @rowindex := @rowindex + 1 AS rn, data FROM table) main"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("问题SQL模式测试:\n\n");

            for (int i = 0; i < problematicSqls.length; i++) {
                String sql = problematicSqls[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql).append("\n\n");

                // 使用智能转换器
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换结果: ").append(converted).append("\n\n");

                // 检查问题模式
                boolean hasProblems = false;
                if (converted.contains("ROW_NUMBER() OVER") && converted.contains(":=")) {
                    result.append("❌ 发现问题: ROW_NUMBER() OVER ... :=\n");
                    hasProblems = true;
                }
                if (converted.contains("SELECTROW_NUMBER")) {
                    result.append("❌ 发现问题: SELECTROW_NUMBER连在一起\n");
                    hasProblems = true;
                }
                if (converted.toLowerCase().contains("@rowindex")) {
                    result.append("⚠️ 警告: 仍包含@rowindex\n");
                }

                if (!hasProblems && !converted.toLowerCase().contains("@rowindex")) {
                    result.append("✅ 转换正常\n");
                }

                result.append("\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 问题模式测试失败: {}", e.getMessage(), e);
            return "问题模式测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试SQL格式修复
     */
    @GetMapping("/test-format-fixes")
    public String testFormatFixes() {
        log.info("🧪 测试SQL格式修复...");

        String[] formatTestSqls = {
            // 1. IN子句格式问题
            "SELECT * FROM table WHERE org_type_child IN (10280 304,\n                                                                                      10280309,\n                                                                                      10280314)",

            // 2. 负数初始化问题
            "SELECT data FROM table, (Select @rowindex:=-1) b",

            // 3. ROW_NUMBER语法问题
            "SELECT @rowindex := @rowindex + 1 AS rowindex FROM table",

            // 4. 复杂格式问题
            "SELECT\n    @rowindex := @rowindex + 1 AS rn,\n    data\nFROM\n    table,\n    (SELECT @rowindex := 0) r"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("SQL格式修复测试:\n\n");

            for (int i = 0; i < formatTestSqls.length; i++) {
                String sql = formatTestSqls[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql.replaceAll("\\n", "\\\\n")).append("\n\n");

                // 使用智能转换器
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换结果: ").append(converted).append("\n\n");

                // 格式验证
                boolean hasFormatIssues = false;

                if (converted.contains("ROW_NUMBER OVER")) {
                    result.append("❌ 问题: ROW_NUMBER和OVER之间缺少括号\n");
                    hasFormatIssues = true;
                }

                if (converted.contains("ROW_NUMBER() OVER") && converted.contains(":=")) {
                    result.append("❌ 问题: ROW_NUMBER后面仍有:=\n");
                    hasFormatIssues = true;
                }

                if (converted.contains("\n") || converted.contains("\r")) {
                    result.append("❌ 问题: 仍包含换行符\n");
                    hasFormatIssues = true;
                }

                if (converted.matches(".*\\s{2,}.*")) {
                    result.append("⚠️ 警告: 包含多余空格\n");
                }

                if (converted.toLowerCase().contains("@")) {
                    result.append("⚠️ 警告: 仍包含@符号\n");
                }

                // 检查IN子句格式
                if (sql.contains("IN (") && converted.contains("IN (")) {
                    if (!converted.matches(".*IN \\([^)]*\\).*")) {
                        result.append("❌ 问题: IN子句格式错误\n");
                        hasFormatIssues = true;
                    }
                }

                if (!hasFormatIssues && !converted.toLowerCase().contains("@")) {
                    result.append("✅ 格式修复成功\n");
                }

                result.append("\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 格式修复测试失败: {}", e.getMessage(), e);
            return "格式修复测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试字段名保护
     */
    @GetMapping("/test-field-name-protection")
    public String testFieldNameProtection() {
        log.info("🧪 测试字段名保护...");

        String[] fieldNameTestSqls = {
            // 1. orgId字段测试
            "SELECT orgId, orgName FROM organization WHERE orgId = 123",

            // 2. 各种包含关键字的字段名
            "SELECT userId, userName, orderDate, groupType FROM users",

            // 3. 复杂的字段名组合
            "SELECT orgId, fromDate, whereClause, andCondition, orCondition FROM table",

            // 4. 带有@rowindex的复杂查询
            "SELECT @rowindex := @rowindex + 1 AS rn, orgId, fromDate FROM table, (SELECT @rowindex := 0) r",

            // 5. IN子句中的字段名
            "SELECT * FROM table WHERE orgId IN (1, 2, 3) AND fromDate > '2024-01-01'"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("字段名保护测试:\n\n");

            for (int i = 0; i < fieldNameTestSqls.length; i++) {
                String sql = fieldNameTestSqls[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql).append("\n\n");

                // 使用智能转换器
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换结果: ").append(converted).append("\n\n");

                // 检查字段名是否被误替换
                boolean hasFieldNameIssues = false;

                if (converted.contains("OR gId")) {
                    result.append("❌ 问题: orgId被替换为OR gId\n");
                    hasFieldNameIssues = true;
                }

                if (converted.contains("FROM Date")) {
                    result.append("❌ 问题: fromDate被替换为FROM Date\n");
                    hasFieldNameIssues = true;
                }

                if (converted.contains("WHERE Clause")) {
                    result.append("❌ 问题: whereClause被替换为WHERE Clause\n");
                    hasFieldNameIssues = true;
                }

                if (converted.contains("AND Condition")) {
                    result.append("❌ 问题: andCondition被替换为AND Condition\n");
                    hasFieldNameIssues = true;
                }

                if (converted.contains("OR Condition")) {
                    result.append("❌ 问题: orCondition被替换为OR Condition\n");
                    hasFieldNameIssues = true;
                }

                if (converted.contains("ORDER Count")) {
                    result.append("❌ 问题: orderCount被替换为ORDER Count\n");
                    hasFieldNameIssues = true;
                }

                if (converted.contains("GROUP Type")) {
                    result.append("❌ 问题: groupType被替换为GROUP Type\n");
                    hasFieldNameIssues = true;
                }

                // 检查是否保持了原始字段名
                String[] originalFields = {"orgId", "orgName", "userId", "userName", "orderDate", "groupType",
                                         "fromDate", "whereClause", "andCondition", "orCondition"};

                for (String field : originalFields) {
                    if (sql.contains(field) && !converted.contains(field)) {
                        result.append("❌ 问题: 字段 ").append(field).append(" 丢失或被修改\n");
                        hasFieldNameIssues = true;
                    }
                }

                if (!hasFieldNameIssues) {
                    result.append("✅ 字段名保护成功\n");
                }

                result.append("\n");
            }

            // 特别测试orgId问题
            result.append("=== 特别测试orgId问题 ===\n");
            String orgIdTest = "SELECT orgId FROM organization WHERE orgId = 123";
            String orgIdConverted = ComplexSqlConverter.smartConvert(orgIdTest);
            result.append("原始: ").append(orgIdTest).append("\n");
            result.append("转换: ").append(orgIdConverted).append("\n");

            if (orgIdConverted.contains("OR gId")) {
                result.append("❌ 确认问题: orgId被错误替换为OR gId\n");
            } else if (orgIdConverted.contains("orgId")) {
                result.append("✅ 确认修复: orgId字段名保持完整\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 字段名保护测试失败: {}", e.getMessage(), e);
            return "字段名保护测试失败: " + e.getMessage();
        }
    }

    /**
     * 调试orgId具体问题
     */
    @GetMapping("/debug-orgid")
    public String debugOrgId() {
        log.info("🔍 调试orgId具体问题...");

        String testSql = "SELECT orgId FROM organization WHERE orgId = 123";

        try {
            StringBuilder result = new StringBuilder();
            result.append("orgId问题调试:\n\n");
            result.append("原始SQL: ").append(testSql).append("\n\n");

            // 逐步测试每个转换
            String step1 = testSql;

            // 1. 基础转换
            String step2 = SqlCompatibilityUtil.convertSql(step1);
            result.append("SqlCompatibilityUtil.convertSql: ").append(step2).append("\n");
            result.append("包含orgId: ").append(step2.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(step2.contains("OR gId")).append("\n\n");

            // 2. 复杂转换
            String step3 = ComplexSqlConverter.convertComplexSql(step1);
            result.append("ComplexSqlConverter.convertComplexSql: ").append(step3).append("\n");
            result.append("包含orgId: ").append(step3.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(step3.contains("OR gId")).append("\n\n");

            // 3. 智能转换
            String step4 = ComplexSqlConverter.smartConvert(step1);
            result.append("ComplexSqlConverter.smartConvert: ").append(step4).append("\n");
            result.append("包含orgId: ").append(step4.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(step4.contains("OR gId")).append("\n\n");

            // 4. 手动测试正则表达式
            String manual1 = testSql.replaceAll("(?i)OR(?=\\w)", "OR ");
            result.append("手动测试 OR(?=\\w): ").append(manual1).append("\n");
            result.append("包含orgId: ").append(manual1.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(manual1.contains("OR gId")).append("\n\n");

            String manual2 = testSql.replaceAll("(?i)\\bOR(?=\\w)", "OR ");
            result.append("手动测试 \\bOR(?=\\w): ").append(manual2).append("\n");
            result.append("包含orgId: ").append(manual2.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(manual2.contains("OR gId")).append("\n\n");

            // 5. 测试复杂SQL
            String complexSql = "select scoreMedian1 from(SELECT AVG(g.score1) scoreMedian1 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM(select score1 from(select sum(NVL(tt1.total, 0)) as score1 from table1 tt1) grades) grades, (SELECT @rowindex := 0) r ORDER BY grades.score1) g), (select orgId from organization where orgId = 123) org";

            result.append("复杂SQL测试:\n");
            result.append("原始: ").append(complexSql.substring(0, Math.min(100, complexSql.length()))).append("...\n");

            String complexResult = ComplexSqlConverter.smartConvert(complexSql);
            result.append("转换结果: ").append(complexResult.substring(0, Math.min(100, complexResult.length()))).append("...\n");
            result.append("包含orgId: ").append(complexResult.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(complexResult.contains("OR gId")).append("\n");

            if (complexResult.contains("OR gId")) {
                result.append("❌ 确认问题: 复杂SQL中orgId被替换为OR gId\n");
            } else {
                result.append("✅ 复杂SQL中orgId保持完整\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ orgId调试失败: {}", e.getMessage(), e);
            return "orgId调试失败: " + e.getMessage();
        }
    }

    /**
     * 验证字段名保护修复
     */
    @GetMapping("/verify-field-protection-fix")
    public String verifyFieldProtectionFix() {
        log.info("🔍 验证字段名保护修复...");

        String[] testCases = {
            "SELECT tmp1.orgId FROM table tmp1",
            "SELECT a_or.idf FROM table",
            "SELECT t.fromDate, a.orderBy FROM table t, table2 a",
            "WHERE tmp1.orgId = 123 AND t.fromDate > '2024-01-01'",
            "select L.orgId,score1 from table L WHERE L.orgId IN (1,2,3)"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("字段名保护修复验证:\n\n");

            for (int i = 0; i < testCases.length; i++) {
                String sql = testCases[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql).append("\n");

                // 使用修复后的转换器
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换结果: ").append(converted).append("\n");

                // 检查关键字段名是否保持完整
                String[] fieldsToCheck = {"orgId", "fromDate", "orderBy", "idf"};
                boolean allFieldsPreserved = true;

                for (String field : fieldsToCheck) {
                    if (sql.contains(field) && !converted.contains(field)) {
                        result.append("❌ 问题: 字段 ").append(field).append(" 丢失\n");
                        allFieldsPreserved = false;
                    }
                }

                // 检查是否有错误的替换
                if (converted.contains("OR gId") || converted.contains("FROM Date") ||
                    converted.contains("ORDER By") || converted.contains("GROUP Type")) {
                    result.append("❌ 问题: 发现错误的字段名替换\n");
                    allFieldsPreserved = false;
                }

                if (allFieldsPreserved) {
                    result.append("✅ 字段名保护成功\n");
                }

                result.append("\n");
            }

            // 特别测试复杂SQL
            result.append("=== 复杂SQL测试 ===\n");
            String complexSql = "select L.orgId,score1 partyIndex from(select sum(NVL(tt1.total, 0)) as score1 from table1 tt1) L WHERE L.orgId IN (1,2,3) ORDER BY L.orgId";
            result.append("复杂SQL: ").append(complexSql.substring(0, Math.min(80, complexSql.length()))).append("...\n");

            String complexResult = ComplexSqlConverter.smartConvert(complexSql);
            result.append("转换结果: ").append(complexResult.substring(0, Math.min(80, complexResult.length()))).append("...\n");

            if (complexResult.contains("orgId")) {
                result.append("✅ 复杂SQL中orgId保持完整\n");
            } else {
                result.append("❌ 复杂SQL中orgId丢失\n");
            }

            if (complexResult.contains("OR gId")) {
                result.append("❌ 复杂SQL中发现错误替换\n");
            } else {
                result.append("✅ 复杂SQL中没有错误替换\n");
            }

            result.append("\n说明: 为了保护字段名不被误替换，我们暂时禁用了SQL关键字的格式化功能。\n");
            result.append("这样可以确保 tmp1.orgId、a_or.idf 等字段名不会被破坏。");

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 字段名保护验证失败: {}", e.getMessage(), e);
            return "字段名保护验证失败: " + e.getMessage();
        }
    }

    /**
     * 测试关键字连接问题修复
     */
    @GetMapping("/test-keyword-concatenation-fix")
    public String testKeywordConcatenationFix() {
        log.info("🔍 测试关键字连接问题修复...");

        String[] testCases = {
            // 可能导致SELECTROW_NUMBER的情况
            "SELECT @rowindex := @rowindex + 1 AS rowindex FROM table",
            "select @rowindex := @rowindex + 1 AS rn FROM users",

            // 复杂的嵌套情况
            "SELECT * FROM (SELECT @rowindex := @rowindex + 1 AS rn, data FROM table, (SELECT @rowindex := 0) r) ranked",

            // 您的复杂SQL
            "select scoreMedian1 from(SELECT AVG(g.score1) scoreMedian1 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM(select score1 from table1) grades, (SELECT @rowindex := 0) r ORDER BY grades.score1) g)"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("关键字连接问题修复测试:\n\n");

            for (int i = 0; i < testCases.length; i++) {
                String sql = testCases[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql.substring(0, Math.min(80, sql.length()))).append("...\n");

                // 使用智能转换器
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换结果: ").append(converted.substring(0, Math.min(80, converted.length()))).append("...\n");

                // 检查关键字连接问题
                boolean hasIssues = false;

                if (converted.contains("SELECTROW_NUMBER")) {
                    result.append("❌ 问题: 发现SELECTROW_NUMBER连接\n");
                    hasIssues = true;
                }

                if (converted.contains("ROW_NUMBEROVER")) {
                    result.append("❌ 问题: 发现ROW_NUMBEROVER连接\n");
                    hasIssues = true;
                }

                if (converted.contains("ROW_NUMBER OVER")) {
                    result.append("❌ 问题: ROW_NUMBER和OVER之间缺少括号\n");
                    hasIssues = true;
                }

                // 检查正确的语法
                if (converted.contains("SELECT ROW_NUMBER() OVER(")) {
                    result.append("✅ 正确: SELECT ROW_NUMBER() OVER( 语法正确\n");
                } else if (converted.contains("ROW_NUMBER()")) {
                    result.append("✅ 部分正确: 包含ROW_NUMBER()\n");
                }

                if (!hasIssues && converted.contains("ROW_NUMBER()")) {
                    result.append("✅ 关键字连接修复成功\n");
                }

                result.append("\n");
            }

            // 手动测试具体的连接问题
            result.append("=== 手动测试连接修复 ===\n");
            String[] manualTests = {
                "SELECTROW_NUMBER() OVER(ORDER BY 1)",
                "SELECT ROW_NUMBEROVER(ORDER BY 1)",
                "SELECT ROW_NUMBER OVER(ORDER BY 1)",
                "SELECT ROW_NUMBER()OVER(ORDER BY 1)"
            };

            for (String test : manualTests) {
                result.append("原始: ").append(test).append("\n");

                // 应用我们的修复
                String fixed = test;
                fixed = fixed.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");
                fixed = fixed.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");
                fixed = fixed.replaceAll("(?i)ROW_NUMBER\\s*OVER", "ROW_NUMBER() OVER");
                fixed = fixed.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");

                result.append("修复: ").append(fixed).append("\n");

                if (fixed.contains("SELECT ROW_NUMBER() OVER(")) {
                    result.append("✅ 修复成功\n");
                } else {
                    result.append("❌ 仍有问题\n");
                }

                result.append("\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 关键字连接测试失败: {}", e.getMessage(), e);
            return "关键字连接测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试初始化语句清理修复
     */
    @GetMapping("/test-initialization-cleanup-fix")
    public String testInitializationCleanupFix() {
        log.info("🔍 测试初始化语句清理修复...");

        String[] testCases = {
            // 各种初始化模式
            "SELECT data FROM table, (SELECT @rowindex := 0) r",
            "SELECT data FROM table, (SELECT @rowindex := -1) b",
            "SELECT data FROM table, (Select @rowindex:=-1) b",
            "SELECT data FROM table, (SELECT @rowindex = 1) init",
            "SELECT data FROM table, (SELECT @row := 0, @total := 0) vars",

            // 复杂的嵌套情况
            "SELECT * FROM (SELECT @rowindex := @rowindex + 1 AS rn, data FROM table, (SELECT @rowindex := -1) b) ranked",

            // 您提到的具体情况
            "select scoreMedian1 from(SELECT AVG(g.score1) scoreMedian1 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM(select score1 from table1) grades, (Select @rowindex:=-1) b ORDER BY grades.score1) g)"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("初始化语句清理修复测试:\n\n");

            for (int i = 0; i < testCases.length; i++) {
                String sql = testCases[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql.substring(0, Math.min(80, sql.length()))).append("...\n");

                // 使用智能转换器
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换结果: ").append(converted.substring(0, Math.min(80, converted.length()))).append("...\n");

                // 检查问题模式
                boolean hasIssues = false;

                if (converted.contains("(SELECT 1 := -1)") || converted.contains("(SELECT 1 := 0)") ||
                    converted.contains("(SELECT 1 := 1)")) {
                    result.append("❌ 问题: 发现错误的初始化残留 (SELECT 1 := 数字)\n");
                    hasIssues = true;
                }

                if (converted.contains("@rowindex") && converted.contains(":=")) {
                    result.append("⚠️ 警告: 仍包含@rowindex初始化语句\n");
                }

                // 检查是否正确移除了初始化
                if (!converted.contains("(SELECT @") && !converted.contains("(Select @")) {
                    result.append("✅ 正确: 初始化语句已完全移除\n");
                } else if (!hasIssues) {
                    result.append("✅ 部分正确: 没有错误的残留\n");
                }

                result.append("\n");
            }

            // 手动测试具体的问题情况
            result.append("=== 手动测试问题修复 ===\n");
            String problemSql = "(Select @rowindex:=-1) b";
            result.append("问题SQL: ").append(problemSql).append("\n");

            // 测试初始化移除
            String step1 = problemSql;
            step1 = step1.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*", "");
            result.append("移除初始化: ").append(step1).append("\n");

            // 如果没有逗号开头，尝试其他模式
            if (step1.equals(problemSql)) {
                step1 = step1.replaceAll("(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*", "");
                result.append("无逗号移除: ").append(step1).append("\n");
            }

            // 测试残留清理
            if (step1.contains("@rowindex")) {
                String step2 = step1.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
                result.append("残留清理: ").append(step2).append("\n");

                if (step2.contains("(SELECT 1 := -1)")) {
                    result.append("❌ 仍有问题: 产生了错误的语法\n");
                } else {
                    result.append("✅ 残留清理正确\n");
                }
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 初始化清理测试失败: {}", e.getMessage(), e);
            return "初始化清理测试失败: " + e.getMessage();
        }
    }

    /**
     * 专门测试复杂SQL的修复
     */
    @GetMapping("/test-complex-sql-detailed")
    public String testComplexSqlDetailed() {
        log.info("🔍 专门测试复杂SQL的修复...");

        // 使用与test-complex-sql相同的SQL
        String complexSql = "select scoreMedian1, scoreMedian2, scoreMedian3 from(" +
                "SELECT AVG(g.score1) scoreMedian1 " +
                "FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 " +
                "FROM(select score1 from(select sum(NVL(tt1.total, 0)) as score1 from table1 tt1) grades) grades, " +
                "(Select @rowindex:=-1) b " +
                "ORDER BY grades.score1) g), " +
                "(select AVG(g.score2) scoreMedian2 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score2 AS score2 FROM(select score2 from(select sum(NVL(tt2.total, 0)) as score2 from table2 tt2) grades) grades, (Select @rowindex:=-1) b ORDER BY grades.score2) g), " +
                "(select AVG(g.score3) scoreMedian3 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score3 AS score3 FROM(select score3 from(select sum(NVL(tt3.total, 0)) as score3 from table3 tt3) grades) grades, (Select @rowindex:=-1) b ORDER BY grades.score3) g)";

        try {
            StringBuilder result = new StringBuilder();
            result.append("复杂SQL详细测试:\n\n");
            result.append("原始SQL长度: ").append(complexSql.length()).append("\n");
            result.append("原始SQL前100字符: ").append(complexSql.substring(0, Math.min(100, complexSql.length()))).append("...\n\n");

            // 统计原始SQL中的问题模式
            int rowindexCount = countOccurrences(complexSql, "@rowindex");
            int initCount = countOccurrences(complexSql, "(Select @rowindex:=-1) b");
            result.append("原始SQL统计:\n");
            result.append("- @rowindex出现次数: ").append(rowindexCount).append("\n");
            result.append("- 初始化语句次数: ").append(initCount).append("\n\n");

            // 逐步转换测试
            result.append("=== 逐步转换测试 ===\n");

            // 步骤1: 基础转换
            String step1 = SqlCompatibilityUtil.convertSql(complexSql);
            result.append("步骤1 - 基础转换:\n");
            result.append("长度: ").append(step1.length()).append("\n");
            result.append("包含@rowindex: ").append(step1.contains("@rowindex")).append("\n");
            result.append("包含(Select @rowindex:=-1): ").append(step1.contains("(Select @rowindex:=-1)")).append("\n");
            result.append("包含(SELECT 1 := -1): ").append(step1.contains("(SELECT 1 := -1)")).append("\n");
            result.append("包含ROW_NUMBER: ").append(step1.contains("ROW_NUMBER")).append("\n\n");

            // 步骤2: 复杂转换
            String step2 = ComplexSqlConverter.convertComplexSql(complexSql);
            result.append("步骤2 - 复杂转换:\n");
            result.append("长度: ").append(step2.length()).append("\n");
            result.append("包含@rowindex: ").append(step2.contains("@rowindex")).append("\n");
            result.append("包含(Select @rowindex:=-1): ").append(step2.contains("(Select @rowindex:=-1)")).append("\n");
            result.append("包含(SELECT 1 := -1): ").append(step2.contains("(SELECT 1 := -1)")).append("\n");
            result.append("包含ROW_NUMBER: ").append(step2.contains("ROW_NUMBER")).append("\n\n");

            // 步骤3: 智能转换
            String step3 = ComplexSqlConverter.smartConvert(complexSql);
            result.append("步骤3 - 智能转换:\n");
            result.append("长度: ").append(step3.length()).append("\n");
            result.append("包含@rowindex: ").append(step3.contains("@rowindex")).append("\n");
            result.append("包含(Select @rowindex:=-1): ").append(step3.contains("(Select @rowindex:=-1)")).append("\n");
            result.append("包含(SELECT 1 := -1): ").append(step3.contains("(SELECT 1 := -1)")).append("\n");
            result.append("包含ROW_NUMBER: ").append(step3.contains("ROW_NUMBER")).append("\n");
            result.append("包含SELECTROW_NUMBER: ").append(step3.contains("SELECTROW_NUMBER")).append("\n");
            result.append("包含tmp1.OR gId: ").append(step3.contains("tmp1.OR gId")).append("\n\n");

            // 显示最终结果的前200字符
            result.append("最终结果前200字符:\n");
            result.append(step3.substring(0, Math.min(200, step3.length()))).append("...\n\n");

            // 问题检查
            result.append("=== 问题检查 ===\n");
            boolean hasIssues = false;

            if (step3.contains("(SELECT 1 := -1)")) {
                result.append("❌ 问题: 发现错误的初始化残留\n");
                hasIssues = true;
            }

            if (step3.contains("SELECTROW_NUMBER")) {
                result.append("❌ 问题: 发现关键字连接\n");
                hasIssues = true;
            }

            if (step3.contains("tmp1.OR gId") || step3.contains("OR gId")) {
                result.append("❌ 问题: 发现字段名被破坏\n");
                hasIssues = true;
            }

            if (step3.contains("@rowindex")) {
                result.append("⚠️ 警告: 仍包含@rowindex\n");
            }

            if (!hasIssues && step3.contains("ROW_NUMBER()")) {
                result.append("✅ 转换成功: 包含正确的ROW_NUMBER()语法\n");
            }

            if (!hasIssues) {
                result.append("✅ 复杂SQL转换成功，没有发现问题\n");
            } else {
                result.append("❌ 复杂SQL转换仍有问题需要修复\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 复杂SQL详细测试失败: {}", e.getMessage(), e);
            return "复杂SQL详细测试失败: " + e.getMessage();
        }
    }

    /**
     * 计算字符串出现次数
     */
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }

    /**
     * 测试具体的grades,(Select @rowindex:=-1) b问题
     */
    @GetMapping("/test-specific-grades-issue")
    public String testSpecificGradesIssue() {
        log.info("🔍 测试具体的grades,(Select @rowindex:=-1) b问题...");

        String[] testCases = {
            // 具体的问题模式
            "grades,(Select @rowindex:=-1) b",
            "FROM grades,(Select @rowindex:=-1) b ORDER BY",
            "SELECT * FROM table1 grades,(Select @rowindex:=-1) b WHERE",

            // 类似的模式
            "table,(SELECT @rowindex := 0) r",
            "data,(select @rowindex:=-1) init",
            "users, (Select @rowindex:=-1) b",  // 有空格的情况

            // 完整的SQL片段
            "FROM(select score1 from table1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("具体grades问题测试:\n\n");

            for (int i = 0; i < testCases.length; i++) {
                String sql = testCases[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始: ").append(sql).append("\n");

                // 手动测试各种初始化移除模式
                String testResult = sql;

                // 测试紧密连接的逗号模式
                String pattern1 = "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*";
                String after1 = testResult.replaceAll(pattern1, "");
                if (!after1.equals(testResult)) {
                    result.append("模式1匹配: ").append(pattern1).append("\n");
                    result.append("结果1: ").append(after1).append("\n");
                    testResult = after1;
                } else {
                    // 测试带空格的模式
                    String pattern2 = "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*";
                    String after2 = testResult.replaceAll(pattern2, "");
                    if (!after2.equals(testResult)) {
                        result.append("模式2匹配: ").append(pattern2).append("\n");
                        result.append("结果2: ").append(after2).append("\n");
                        testResult = after2;
                    } else {
                        result.append("⚠️ 没有模式匹配\n");
                    }
                }

                // 使用智能转换器测试
                String smartResult = ComplexSqlConverter.smartConvert(sql);
                result.append("智能转换: ").append(smartResult).append("\n");

                // 检查问题
                if (smartResult.contains("(SELECT 1 := -1)") || smartResult.contains("(SELECT 1 := - 1)")) {
                    result.append("❌ 问题: 仍有错误的初始化残留\n");
                } else if (!smartResult.contains("(Select @rowindex:=-1)")) {
                    result.append("✅ 成功: 初始化语句已移除\n");
                } else {
                    result.append("⚠️ 警告: 初始化语句未移除\n");
                }

                result.append("\n");
            }

            // 特别测试您发现的具体问题
            result.append("=== 特别测试您发现的问题 ===\n");
            String specificIssue = "grades,(Select @rowindex:=-1) b";
            result.append("具体问题: ").append(specificIssue).append("\n");

            // 测试所有可能的模式
            String[] patterns = {
                "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
                "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
                "(?i)\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*"
            };

            for (int i = 0; i < patterns.length; i++) {
                String pattern = patterns[i];
                String testResult = specificIssue.replaceAll(pattern, "");
                result.append("模式 ").append(i + 1).append(": ").append(pattern).append("\n");
                result.append("结果 ").append(i + 1).append(": ").append(testResult).append("\n");

                if (!testResult.equals(specificIssue)) {
                    result.append("✅ 模式 ").append(i + 1).append(" 匹配成功\n");
                    break;
                } else {
                    result.append("❌ 模式 ").append(i + 1).append(" 不匹配\n");
                }
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 具体grades问题测试失败: {}", e.getMessage(), e);
            return "具体grades问题测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试精确字符串替换修复
     */
    @GetMapping("/test-exact-string-replacement")
    public String testExactStringReplacement() {
        log.info("🔍 测试精确字符串替换修复...");

        String[] testCases = {
            // 您发现的具体问题
            "grades,(Select @rowindex:=-1) b",
            "FROM grades,(Select @rowindex:=-1) b ORDER BY",

            // 类似的模式
            "table,(Select @rowindex:=-1) b",
            "data, (Select @rowindex:=-1) b",  // 有空格
            "users,(SELECT @rowindex:=-1) b",  // 全大写

            // 完整的SQL片段
            "FROM(select score1 from table1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("精确字符串替换测试:\n\n");

            for (int i = 0; i < testCases.length; i++) {
                String sql = testCases[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始: ").append(sql).append("\n");

                // 手动执行精确替换
                String manual = sql;
                manual = manual.replace(",(Select @rowindex:=-1) b", "");
                manual = manual.replace(", (Select @rowindex:=-1) b", "");
                manual = manual.replace(",(SELECT @rowindex:=-1) b", "");
                manual = manual.replace(", (SELECT @rowindex:=-1) b", "");

                result.append("手动精确替换: ").append(manual).append("\n");

                // 使用智能转换器
                String smart = ComplexSqlConverter.smartConvert(sql);
                result.append("智能转换: ").append(smart).append("\n");

                // 检查结果
                boolean manualSuccess = !manual.contains("(Select @rowindex:=-1)") &&
                                       !manual.contains("(SELECT @rowindex:=-1)") &&
                                       !manual.contains("(SELECT 1 := -1)");

                boolean smartSuccess = !smart.contains("(Select @rowindex:=-1)") &&
                                      !smart.contains("(SELECT @rowindex:=-1)") &&
                                      !smart.contains("(SELECT 1 := -1)");

                result.append("手动替换成功: ").append(manualSuccess ? "✅" : "❌").append("\n");
                result.append("智能转换成功: ").append(smartSuccess ? "✅" : "❌").append("\n");

                if (smart.contains("(SELECT 1 := -1)") || smart.contains("(SELECT 1 := - 1)")) {
                    result.append("❌ 警告: 智能转换仍产生错误语法\n");
                }

                result.append("\n");
            }

            // 特别测试您发现的问题
            result.append("=== 特别测试您的具体问题 ===\n");
            String yourIssue = "grades,(Select @rowindex:=-1) b";
            result.append("您的问题: ").append(yourIssue).append("\n");

            // 精确替换
            String fixed = yourIssue.replace(",(Select @rowindex:=-1) b", "");
            result.append("精确替换: '").append(fixed).append("'\n");

            if (fixed.equals("grades")) {
                result.append("✅ 精确替换成功，完全移除了初始化语句\n");
            } else {
                result.append("❌ 精确替换失败\n");
            }

            // 使用转换器测试
            String converted = ComplexSqlConverter.smartConvert(yourIssue);
            result.append("转换器结果: '").append(converted).append("'\n");

            if (converted.equals("grades") || (!converted.contains("@rowindex") && !converted.contains("(SELECT 1 := -1)"))) {
                result.append("✅ 转换器修复成功\n");
            } else {
                result.append("❌ 转换器仍有问题\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 精确字符串替换测试失败: {}", e.getMessage(), e);
            return "精确字符串替换测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试多空格问题修复
     */
    @GetMapping("/test-multiple-spaces-fix")
    public String testMultipleSpacesFix() {
        log.info("🔍 测试多空格问题修复...");

        try {
            StringBuilder result = new StringBuilder();
            result.append("多空格问题修复测试:\n\n");

            // 从实际的testComplexSql中提取的问题模式
            String actualPattern = "grades,(Select @rowindex:=-1) b  ORDER BY grades.score1";
            result.append("实际问题模式: ").append(actualPattern).append("\n");
            result.append("注意: 'b' 后面有两个空格\n\n");

            // 测试各种修复方法
            result.append("=== 测试修复方法 ===\n");

            // 方法1: 简单字符串替换（可能失败）
            String method1 = actualPattern.replace(",(Select @rowindex:=-1) b", "");
            result.append("方法1 - 简单替换: ").append(method1).append("\n");
            result.append("成功: ").append(!method1.contains("@rowindex") ? "✅" : "❌").append("\n\n");

            // 方法2: 处理多空格的正则表达式
            String method2 = actualPattern.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
            result.append("方法2 - 正则多空格: ").append(method2).append("\n");
            result.append("成功: ").append(!method2.contains("@rowindex") ? "✅" : "❌").append("\n\n");

            // 方法3: 更宽松的正则表达式
            String method3 = actualPattern.replaceAll("(?i),\\s*\\(Select @rowindex:=-1\\) b\\s*", " ");
            result.append("方法3 - 宽松正则: ").append(method3).append("\n");
            result.append("成功: ").append(!method3.contains("@rowindex") ? "✅" : "❌").append("\n\n");

            // 测试智能转换器
            result.append("=== 测试智能转换器 ===\n");
            String smartResult = ComplexSqlConverter.smartConvert(actualPattern);
            result.append("智能转换结果: ").append(smartResult).append("\n");

            boolean smartSuccess = !smartResult.contains("@rowindex") &&
                                  !smartResult.contains("(SELECT 1 := -1)") &&
                                  !smartResult.contains("(SELECT 1 := - 1)");
            result.append("智能转换成功: ").append(smartSuccess ? "✅" : "❌").append("\n\n");

            if (smartResult.contains("(SELECT 1 := -1)") || smartResult.contains("(SELECT 1 := - 1)")) {
                result.append("❌ 警告: 智能转换仍产生错误语法\n\n");
            }

            // 测试更多空格变体
            result.append("=== 测试空格变体 ===\n");
            String[] variants = {
                "grades,(Select @rowindex:=-1) b ORDER",      // 一个空格
                "grades,(Select @rowindex:=-1) b  ORDER",     // 两个空格
                "grades,(Select @rowindex:=-1) b   ORDER",    // 三个空格
                "grades, (Select @rowindex:=-1) b  ORDER",    // 逗号后有空格
            };

            for (int i = 0; i < variants.length; i++) {
                String variant = variants[i];
                result.append("变体 ").append(i + 1).append(": ").append(variant).append("\n");

                String variantResult = ComplexSqlConverter.smartConvert(variant);
                result.append("转换结果: ").append(variantResult).append("\n");

                boolean variantSuccess = !variantResult.contains("@rowindex") &&
                                       !variantResult.contains("(SELECT 1 := -1)");
                result.append("成功: ").append(variantSuccess ? "✅" : "❌").append("\n\n");
            }

            // 测试完整的复杂SQL
            result.append("=== 测试完整复杂SQL ===\n");
            result.append("正在测试testComplexSql中的完整SQL...\n");

            // 获取testComplexSql中的默认SQL
            String complexSql = "select L.orgId,score1 partyIndex, score2 businessIndex, score3 InnovationIndex from (select tmp1.org_id orgId, round(NVL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, round(NVL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, round(NVL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3  from (select t1.organization_id org_id,NVL(t2.score1,0) score1,NVL(t2.score2,0) score2,NVL(t2.score3,0) score3 from ( select organization_id  from t_organization  where  organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1 ) t1 LEFT JOIN ( select tt1.score_org_id, sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3  from t_score_org_type_count tt1 INNER JOIN t_organization tt2 on tt1.score_org_id = tt2.organization_id  and tt1.org_id =3 and tt1.score_org_type =1 and tt1.parent_score_type in (1,2,3) and tt2.org_type_child in (10280304,10280309,10280314,10280315,10280319) and tt2.organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and tt2.status=1 GROUP BY tt1.score_org_id ) t2 on t1.organization_id = t2.score_org_id ) tmp1,( select scoreMedian1,scoreMedian2,scoreMedian3 from (  SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1   FROM (select score1 from (  select sum(NVL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and    org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  ) tmmp where score1 !=0  union all  select score1 from (  select DISTINCT sum(NVL(tt1.total,0)) score1 from (select organization_id from t_organiza";

            // 检查是否包含问题模式
            int problemCount = countOccurrences(complexSql, ",(Select @rowindex:=-1) b");
            result.append("复杂SQL中的问题模式数量: ").append(problemCount).append("\n");

            if (problemCount > 0) {
                result.append("✅ 确认复杂SQL中包含问题模式\n");

                // 测试转换
                String complexResult = ComplexSqlConverter.smartConvert(complexSql);
                int remainingProblems = countOccurrences(complexResult, "(SELECT 1 := -1)") +
                                      countOccurrences(complexResult, "(SELECT 1 := - 1)");

                result.append("转换后的错误语法数量: ").append(remainingProblems).append("\n");

                if (remainingProblems == 0) {
                    result.append("✅ 复杂SQL修复成功\n");
                } else {
                    result.append("❌ 复杂SQL仍有问题\n");
                }
            } else {
                result.append("⚠️ 复杂SQL中未找到问题模式\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 多空格问题测试失败: {}", e.getMessage(), e);
            return "多空格问题测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试所有空格变体
     */
    @GetMapping("/test-all-space-variations")
    public String testAllSpaceVariations() {
        log.info("🔍 测试所有空格变体...");

        String[] spaceVariations = {
            // 基本情况
            "grades,(Select @rowindex:=-1) b",

            // 逗号后的空格
            "grades, (Select @rowindex:=-1) b",
            "grades,  (Select @rowindex:=-1) b",
            "grades,   (Select @rowindex:=-1) b",

            // 括号内的空格
            "grades,( Select @rowindex:=-1) b",
            "grades,(  Select @rowindex:=-1) b",
            "grades,(Select  @rowindex:=-1) b",
            "grades,(Select   @rowindex:=-1) b",

            // @rowindex周围的空格
            "grades,(Select @rowindex :=-1) b",
            "grades,(Select @rowindex  :=-1) b",
            "grades,(Select @rowindex: =-1) b",
            "grades,(Select @rowindex := -1) b",
            "grades,(Select @rowindex :=  -1) b",

            // 括号前的空格
            "grades,(Select @rowindex:=-1 ) b",
            "grades,(Select @rowindex:=-1  ) b",
            "grades,(Select @rowindex:=-1   ) b",

            // b前后的空格
            "grades,(Select @rowindex:=-1)  b",
            "grades,(Select @rowindex:=-1)   b",
            "grades,(Select @rowindex:=-1) b ",
            "grades,(Select @rowindex:=-1) b  ",
            "grades,(Select @rowindex:=-1) b   ",

            // 复合空格情况
            "grades,  (  Select   @rowindex  :=  -1  )   b   ",

            // 大小写变体
            "grades,(SELECT @rowindex:=-1) b  ",
            "grades,(select @rowindex:=-1) b   "
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("所有空格变体测试:\n\n");
            result.append("测试 ").append(spaceVariations.length).append(" 种空格变体:\n\n");

            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < spaceVariations.length; i++) {
                String original = spaceVariations[i];
                result.append("变体 ").append(i + 1).append(": '").append(original).append("'\n");

                // 使用智能转换器测试
                String converted = ComplexSqlConverter.smartConvert(original);
                result.append("转换结果: '").append(converted).append("'\n");

                // 检查是否成功
                boolean success = !converted.contains("@rowindex") &&
                                 !converted.contains("(SELECT 1 := -1)") &&
                                 !converted.contains("(SELECT 1 := - 1)");

                if (success) {
                    result.append("✅ 成功\n");
                    successCount++;
                } else {
                    result.append("❌ 失败");
                    if (converted.contains("@rowindex")) {
                        result.append(" (仍包含@rowindex)");
                    }
                    if (converted.contains("(SELECT 1 := -1)")) {
                        result.append(" (错误语法)");
                    }
                    result.append("\n");
                    failCount++;
                }

                result.append("\n");
            }

            // 总结
            result.append("=== 测试总结 ===\n");
            result.append("总测试数: ").append(spaceVariations.length).append("\n");
            result.append("成功: ").append(successCount).append(" ✅\n");
            result.append("失败: ").append(failCount).append(" ❌\n");
            result.append("成功率: ").append(String.format("%.1f", (double)successCount / spaceVariations.length * 100)).append("%\n\n");

            if (failCount == 0) {
                result.append("🎉 所有空格变体都处理成功！\n");
            } else {
                result.append("⚠️ 仍有 ").append(failCount).append(" 种变体需要进一步优化\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 所有空格变体测试失败: {}", e.getMessage(), e);
            return "所有空格变体测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试正常字段名不被误转换
     */
    @GetMapping("/test-normal-field-names")
    public String testNormalFieldNames() {
        log.info("🔍 测试正常字段名不被误转换...");

        String[] normalSqls = {
            // 您提到的情况
            "SELECT deptb AS b FROM table",
            "SELECT deptb as b FROM table",
            "SELECT dept_b AS b FROM table",

            // 其他可能的边界情况
            "SELECT column1, deptb AS b, column2 FROM table",
            "SELECT t.deptb AS b FROM table t",
            "SELECT DISTINCT deptb AS b FROM table",

            // 包含b的各种情况
            "SELECT name AS b FROM users",
            "SELECT status AS b FROM orders",
            "SELECT COUNT(*) AS b FROM items",

            // 复杂的AS b情况
            "SELECT (CASE WHEN status = 1 THEN 'active' ELSE 'inactive' END) AS b FROM table",
            "SELECT CONCAT(first_name, ' ', last_name) AS b FROM users",

            // 多个AS b的情况
            "SELECT col1 AS b, col2 AS c FROM table",
            "SELECT t1.field AS b, t2.field AS c FROM table1 t1, table2 t2",

            // 包含我们的问题模式但在不同上下文中
            "SELECT data FROM table WHERE description LIKE '%Select @rowindex:=-1%'",
            "SELECT '(Select @rowindex:=-1) b' AS test_string FROM table",

            // 正常的子查询
            "SELECT * FROM (SELECT id, name FROM users) b",
            "SELECT * FROM (SELECT COUNT(*) FROM orders) AS b"
        };

        try {
            StringBuilder result = new StringBuilder();
            result.append("正常字段名转换测试:\n\n");
            result.append("测试 ").append(normalSqls.length).append(" 个正常SQL:\n\n");

            int safeCount = 0;
            int changedCount = 0;

            for (int i = 0; i < normalSqls.length; i++) {
                String original = normalSqls[i];
                result.append("测试 ").append(i + 1).append(":\n");
                result.append("原始: ").append(original).append("\n");

                // 使用智能转换器测试
                String converted = ComplexSqlConverter.smartConvert(original);
                result.append("转换: ").append(converted).append("\n");

                // 检查是否被意外修改
                boolean unchanged = original.equals(converted);
                boolean safeChange = !unchanged && !converted.contains("(SELECT 1 := -1)") &&
                                   !converted.contains("ROW_NUMBER") &&
                                   converted.contains(" AS b") || converted.contains(" as b");

                if (unchanged) {
                    result.append("✅ 安全: 未被修改\n");
                    safeCount++;
                } else if (safeChange) {
                    result.append("✅ 安全: 仅进行了安全的转换（如函数名转换）\n");
                    safeCount++;
                } else {
                    result.append("❌ 警告: 发生了意外的修改\n");
                    changedCount++;

                    // 详细分析变化
                    if (!converted.contains(" AS b") && !converted.contains(" as b") && original.contains(" AS b")) {
                        result.append("   - 丢失了 'AS b' 部分\n");
                    }
                    if (converted.contains("ROW_NUMBER")) {
                        result.append("   - 意外添加了ROW_NUMBER\n");
                    }
                    if (converted.contains("(SELECT 1 := -1)")) {
                        result.append("   - 产生了错误语法\n");
                    }
                }

                result.append("\n");
            }

            // 总结
            result.append("=== 测试总结 ===\n");
            result.append("总测试数: ").append(normalSqls.length).append("\n");
            result.append("安全: ").append(safeCount).append(" ✅\n");
            result.append("意外修改: ").append(changedCount).append(" ❌\n");
            result.append("安全率: ").append(String.format("%.1f", (double)safeCount / normalSqls.length * 100)).append("%\n\n");

            if (changedCount == 0) {
                result.append("🎉 所有正常字段名都安全，没有被误转换！\n");
            } else {
                result.append("⚠️ 有 ").append(changedCount).append(" 个正常SQL被意外修改，需要优化正则表达式\n");
            }

            // 特别测试您提到的情况
            result.append("\n=== 特别测试您的情况 ===\n");
            String yourCase = "SELECT deptb AS b FROM table";
            String yourResult = ComplexSqlConverter.smartConvert(yourCase);

            result.append("您的SQL: ").append(yourCase).append("\n");
            result.append("转换结果: ").append(yourResult).append("\n");

            if (yourCase.equals(yourResult)) {
                result.append("✅ 完全安全: 您的SQL不会被转换\n");
            } else if (yourResult.contains("deptb AS b") || yourResult.contains("deptb as b")) {
                result.append("✅ 基本安全: 'deptb AS b' 部分保持完整\n");
            } else {
                result.append("❌ 有问题: 您的SQL被意外修改了\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 正常字段名测试失败: {}", e.getMessage(), e);
            return "正常字段名测试失败: " + e.getMessage();
        }
    }

    /**
     * 直接测试正则表达式
     */
    @GetMapping("/test-regex-directly")
    public String testRegexDirectly() {
        log.info("🔍 直接测试正则表达式...");

        String testSql = "SELECT orgId FROM organization WHERE orgId = 123";

        try {
            StringBuilder result = new StringBuilder();
            result.append("直接正则表达式测试:\n\n");
            result.append("原始SQL: ").append(testSql).append("\n\n");

            // 测试各种正则表达式
            String test1 = testSql.replaceAll("(?i)OR(?=\\w)", "OR ");
            result.append("OR(?=\\w): ").append(test1).append("\n");
            result.append("包含orgId: ").append(test1.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(test1.contains("OR gId")).append("\n\n");

            String test2 = testSql.replaceAll("(?i)\\bOR(?=\\w)", "OR ");
            result.append("\\bOR(?=\\w): ").append(test2).append("\n");
            result.append("包含orgId: ").append(test2.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(test2.contains("OR gId")).append("\n\n");

            String test3 = testSql.replaceAll("(?i)\\bOR\\b", "OR");
            result.append("\\bOR\\b: ").append(test3).append("\n");
            result.append("包含orgId: ").append(test3.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(test3.contains("OR gId")).append("\n\n");

            // 测试复杂SQL中的orgId
            String complexSql = "select L.orgId,score1 from table L WHERE orgId IN (1,2,3)";
            result.append("复杂SQL测试:\n");
            result.append("原始: ").append(complexSql).append("\n");

            String complex1 = complexSql.replaceAll("(?i)OR(?=\\w)", "OR ");
            result.append("OR(?=\\w): ").append(complex1).append("\n");
            result.append("包含orgId: ").append(complex1.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(complex1.contains("OR gId")).append("\n\n");

            String complex2 = complexSql.replaceAll("(?i)\\bOR(?=\\w)", "OR ");
            result.append("\\bOR(?=\\w): ").append(complex2).append("\n");
            result.append("包含orgId: ").append(complex2.contains("orgId")).append("\n");
            result.append("包含OR gId: ").append(complex2.contains("OR gId")).append("\n\n");

            // 确认问题
            if (test1.contains("OR gId") || complex1.contains("OR gId")) {
                result.append("❌ 确认问题: 不使用单词边界会导致orgId被替换\n");
            }

            if (test2.contains("orgId") && complex2.contains("orgId")) {
                result.append("✅ 确认修复: 使用单词边界保护了orgId\n");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("❌ 正则表达式测试失败: {}", e.getMessage(), e);
            return "正则表达式测试失败: " + e.getMessage();
        }
    }
}
