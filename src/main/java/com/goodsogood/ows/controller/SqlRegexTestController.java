package com.goodsogood.ows.controller;

import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * SQL正则表达式测试控制器
 * 用于测试各种@rowindex变体的转换效果
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/test/sql-regex")
@Log4j2
public class SqlRegexTestController {

    /**
     * 测试各种@rowindex变体
     */
    @GetMapping("/test-rowindex-variants")
    public String testRowIndexVariants() {
        log.info("🧪 开始测试@rowindex变体...");
        
        // 测试各种可能的@rowindex写法
        String[] testCases = {
            "@rowindex := @rowindex + 1 AS rowindex",
            "@rowindex : = @rowindex + 1 AS rowindex", 
            "@rowindex:= @rowindex + 1 AS rowindex",
            "@rowindex :=@rowindex + 1 AS rowindex",
            "@rowindex : =@rowindex + 1 AS rowindex",
            "@rowindex  :  =  @rowindex  +  1  AS  rowindex",
            "SELECT @rowindex := @rowindex + 1 AS rowindex",
            "SELECT @rowindex : = @rowindex + 1 AS rowindex"
        };
        
        StringBuilder result = new StringBuilder();
        result.append("@rowindex变体测试结果:\n\n");
        
        for (int i = 0; i < testCases.length; i++) {
            String testCase = testCases[i];
            result.append("测试案例 ").append(i + 1).append(":\n");
            result.append("原始: ").append(testCase).append("\n");
            
            // 测试我们的正则表达式
            String converted = testCase.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", 
                                                 "ROW_NUMBER() OVER(ORDER BY grades.score1) AS $1");
            result.append("转换: ").append(converted).append("\n");
            
            // 检查是否还有@rowindex残留
            if (converted.toLowerCase().contains("@rowindex")) {
                // 进行二次清理
                converted = converted.replaceAll("(?i)@rowindex\\s*:?\\s*=?\\s*@rowindex\\s*\\+?\\s*1?", "ROW_NUMBER() OVER(ORDER BY grades.score1)");
                result.append("二次清理: ").append(converted).append("\n");
            }
            
            result.append("包含@符号: ").append(converted.contains("@")).append("\n\n");
        }
        
        return result.toString();
    }

    /**
     * 测试完整SQL转换
     */
    @GetMapping("/test-full-sql")
    public String testFullSqlConversion(@RequestParam(required = false) String variant) {
        log.info("🧪 测试完整SQL转换...");
        
        String sql;
        if ("variant1".equals(variant)) {
            sql = "SELECT @rowindex : = @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM grades, (SELECT @rowindex := 0) r";
        } else if ("variant2".equals(variant)) {
            sql = "SELECT @rowindex  :  =  @rowindex  +  1  AS  rowindex, grades.score1 AS score1 FROM grades, (SELECT @rowindex : = 0) r";
        } else {
            sql = "SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM grades, (SELECT @rowindex := 0) r";
        }
        
        StringBuilder result = new StringBuilder();
        result.append("完整SQL转换测试:\n\n");
        result.append("原始SQL: ").append(sql).append("\n\n");
        
        // 步骤1: 移除变量初始化
        String step1 = sql.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*0\\s*\\)\\s*\\w*", "");
        result.append("步骤1 - 移除初始化: ").append(step1).append("\n\n");
        
        // 步骤2: 替换@rowindex
        String step2 = step1.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", 
                                       "ROW_NUMBER() OVER(ORDER BY grades.score1) AS $1");
        result.append("步骤2 - 替换@rowindex: ").append(step2).append("\n\n");
        
        // 步骤3: 检查残留
        if (step2.toLowerCase().contains("@rowindex")) {
            String step3 = step2.replaceAll("(?i)@rowindex\\s*:?\\s*=?\\s*@rowindex\\s*\\+?\\s*1?", "ROW_NUMBER() OVER(ORDER BY grades.score1)");
            result.append("步骤3 - 清理残留: ").append(step3).append("\n\n");
            step2 = step3;
        }
        
        // 步骤4: 最终清理
        String finalSql = step2.replaceAll(",\\s*FROM", " FROM").replaceAll("\\s+", " ").trim();
        result.append("最终结果: ").append(finalSql).append("\n\n");
        result.append("包含@符号: ").append(finalSql.contains("@")).append("\n");
        
        return result.toString();
    }
}
