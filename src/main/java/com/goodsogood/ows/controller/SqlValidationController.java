package com.goodsogood.ows.controller;

import com.goodsogood.ows.dmconfig.ComplexSqlConverter;
import com.goodsogood.ows.dmconfig.SqlPatternDetector;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * SQL验证和分析控制器
 * 提供SQL模式分析、转换验证等功能
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/validate/sql")
@Log4j2
public class SqlValidationController {

    /**
     * 分析SQL模式
     */
    @GetMapping("/analyze")
    public String analyzeSql(@RequestParam String sql) {
        log.info("🔍 分析SQL模式: {}", sql);
        
        try {
            SqlPatternDetector.SqlPatternAnalysis analysis = SqlPatternDetector.analyzeSql(sql);
            
            StringBuilder result = new StringBuilder();
            result.append("=== SQL模式分析报告 ===\n\n");
            result.append("原始SQL: ").append(sql).append("\n\n");
            result.append("📊 分析结果:\n");
            result.append("复杂度分数: ").append(analysis.complexityScore).append("\n");
            result.append("转换难度: ").append(analysis.conversionDifficulty).append("\n");
            result.append("推荐策略: ").append(analysis.conversionStrategy).append("\n");
            result.append("嵌套查询: ").append(analysis.hasNestedQueries ? "是" : "否").append("\n");
            result.append("多表查询: ").append(analysis.hasMultipleTables ? "是" : "否").append("\n");
            result.append("包含变量: ").append(analysis.hasVariables ? "是" : "否").append("\n");
            result.append("复杂函数: ").append(analysis.hasComplexFunctions ? "是" : "否").append("\n\n");
            
            if (!analysis.detectedPatterns.isEmpty()) {
                result.append("🔍 检测到的MySQL模式:\n");
                analysis.detectedPatterns.entrySet().stream()
                    .filter(entry -> entry.getValue())
                    .forEach(entry -> result.append("  ✓ ").append(entry.getKey()).append("\n"));
                result.append("\n");
            }
            
            if (!analysis.recommendations.isEmpty()) {
                result.append("💡 转换建议:\n");
                analysis.recommendations.forEach(rec -> 
                    result.append("  • ").append(rec).append("\n"));
                result.append("\n");
            }
            
            // 执行转换测试
            result.append("🔄 转换测试:\n");
            try {
                String converted = ComplexSqlConverter.smartConvert(sql);
                result.append("转换成功: ✅\n");
                result.append("转换结果: ").append(converted).append("\n");
                result.append("包含@符号: ").append(converted.contains("@") ? "❌" : "✅").append("\n");
            } catch (Exception e) {
                result.append("转换失败: ❌\n");
                result.append("错误信息: ").append(e.getMessage()).append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("SQL分析失败: {}", e.getMessage(), e);
            return "SQL分析失败: " + e.getMessage();
        }
    }

    /**
     * 验证转换结果
     */
    @GetMapping("/validate-conversion")
    public String validateConversion(@RequestParam String sql) {
        log.info("🧪 验证SQL转换: {}", sql);
        
        try {
            StringBuilder result = new StringBuilder();
            result.append("=== SQL转换验证报告 ===\n\n");
            
            // 原始SQL分析
            SqlPatternDetector.SqlPatternAnalysis originalAnalysis = SqlPatternDetector.analyzeSql(sql);
            result.append("📋 原始SQL分析:\n");
            result.append("复杂度: ").append(originalAnalysis.complexityScore).append("\n");
            result.append("MySQL模式数量: ").append(originalAnalysis.detectedPatterns.size()).append("\n\n");
            
            // 执行转换
            long startTime = System.currentTimeMillis();
            String convertedSql = ComplexSqlConverter.smartConvert(sql);
            long conversionTime = System.currentTimeMillis() - startTime;
            
            // 转换后SQL分析
            SqlPatternDetector.SqlPatternAnalysis convertedAnalysis = SqlPatternDetector.analyzeSql(convertedSql);
            
            result.append("🔄 转换结果:\n");
            result.append("转换耗时: ").append(conversionTime).append("ms\n");
            result.append("原始SQL: ").append(sql).append("\n");
            result.append("转换SQL: ").append(convertedSql).append("\n\n");
            
            result.append("📊 转换效果评估:\n");
            result.append("复杂度变化: ").append(originalAnalysis.complexityScore)
                  .append(" → ").append(convertedAnalysis.complexityScore).append("\n");
            
            // 检查转换质量
            boolean hasRemainingMysqlPatterns = convertedAnalysis.detectedPatterns.entrySet().stream()
                .anyMatch(entry -> entry.getValue() && isMysqlSpecificPattern(entry.getKey()));
            
            result.append("转换完整性: ").append(hasRemainingMysqlPatterns ? "❌ 不完整" : "✅ 完整").append("\n");
            result.append("语法正确性: ").append(validateSqlSyntax(convertedSql) ? "✅ 正确" : "❌ 可能有问题").append("\n");
            
            // 性能评估
            if (conversionTime > 100) {
                result.append("性能警告: ⚠️ 转换耗时较长 (").append(conversionTime).append("ms)\n");
            } else {
                result.append("性能评估: ✅ 转换效率良好\n");
            }
            
            // 残留问题检查
            if (convertedSql.contains("@")) {
                result.append("残留问题: ❌ 仍包含@符号\n");
            }
            if (convertedSql.toLowerCase().contains("mysql")) {
                result.append("残留问题: ❌ 仍包含MySQL关键字\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("转换验证失败: {}", e.getMessage(), e);
            return "转换验证失败: " + e.getMessage();
        }
    }

    /**
     * 批量测试常见SQL模式
     */
    @GetMapping("/batch-test")
    public String batchTest() {
        log.info("🚀 批量测试常见SQL模式...");
        
        String[] commonPatterns = {
            // 变量模式
            "SELECT @rowindex := @rowindex + 1 AS rn FROM table",
            "SELECT @var := value FROM table",
            
            // 函数模式
            "SELECT IFNULL(col, 'default') FROM table",
            "SELECT DATE_FORMAT(NOW(), '%Y-%m-%d') FROM table",
            "SELECT GROUP_CONCAT(name) FROM table",
            
            // 复杂嵌套
            "SELECT * FROM (SELECT @r := @r + 1 AS rn, t.* FROM table t, (SELECT @r := 0) r) ranked",
            
            // 特殊语法
            "INSERT INTO table (col) VALUES (1) ON DUPLICATE KEY UPDATE col = VALUES(col)",
            "SELECT * FROM table FORCE INDEX (idx) WHERE col = 1",
            
            // JSON和正则
            "SELECT JSON_EXTRACT(data, '$.name') FROM table",
            "SELECT * FROM table WHERE col REGEXP '[0-9]+'",
            
            // 边界情况
            "SELECT @rowindex : = @rowindex + 1 AS rn FROM table",
            "SELECT /* comment */ @var := 1 FROM table"
        };
        
        StringBuilder result = new StringBuilder();
        result.append("=== 批量测试报告 ===\n\n");
        
        int totalTests = commonPatterns.length;
        int passedTests = 0;
        long totalTime = 0;
        
        for (int i = 0; i < commonPatterns.length; i++) {
            String sql = commonPatterns[i];
            result.append("测试 ").append(i + 1).append("/").append(totalTests).append(":\n");
            result.append("SQL: ").append(sql).append("\n");
            
            try {
                long startTime = System.currentTimeMillis();
                String converted = ComplexSqlConverter.smartConvert(sql);
                long endTime = System.currentTimeMillis();
                totalTime += (endTime - startTime);
                
                boolean success = !converted.contains("@rowindex") && 
                                !converted.toLowerCase().contains("ifnull") &&
                                !converted.contains("@");
                
                result.append("结果: ").append(success ? "✅ 通过" : "❌ 失败").append("\n");
                result.append("转换: ").append(converted).append("\n");
                result.append("耗时: ").append(endTime - startTime).append("ms\n\n");
                
                if (success) passedTests++;
                
            } catch (Exception e) {
                result.append("结果: ❌ 异常\n");
                result.append("错误: ").append(e.getMessage()).append("\n\n");
            }
        }
        
        result.append("=== 测试总结 ===\n");
        result.append("总测试数: ").append(totalTests).append("\n");
        result.append("通过数: ").append(passedTests).append("\n");
        result.append("成功率: ").append(String.format("%.1f%%", (double)passedTests/totalTests*100)).append("\n");
        result.append("总耗时: ").append(totalTime).append("ms\n");
        result.append("平均耗时: ").append(String.format("%.1fms", (double)totalTime/totalTests)).append("\n");
        
        return result.toString();
    }

    /**
     * 检查是否为MySQL特有模式
     */
    private boolean isMysqlSpecificPattern(String patternName) {
        return patternName.contains("VARIABLE") || 
               patternName.equals("IFNULL") ||
               patternName.equals("GROUP_CONCAT") ||
               patternName.equals("JSON_EXTRACT") ||
               patternName.equals("ON_DUPLICATE_KEY") ||
               patternName.equals("FORCE_INDEX");
    }

    /**
     * 简单的SQL语法验证
     */
    private boolean validateSqlSyntax(String sql) {
        // 基本语法检查
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        // 检查括号匹配
        int openParens = 0;
        for (char c : sql.toCharArray()) {
            if (c == '(') openParens++;
            else if (c == ')') openParens--;
            if (openParens < 0) return false;
        }
        
        return openParens == 0;
    }
}
