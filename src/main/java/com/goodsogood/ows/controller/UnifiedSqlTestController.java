package com.goodsogood.ows.controller;

import com.goodsogood.ows.dmconfig.UnifiedSqlConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统一SQL转换器测试控制器
 */
@RestController
@RequestMapping("/test/unified-sql")
@Slf4j
public class UnifiedSqlTestController {

    /**
     * 测试统一SQL转换器
     */
    @GetMapping("/test-conversion")
    public String testConversion() {
        log.info("🧪 测试统一SQL转换器...");
        
        String[] testSqls = {
            // 基本的MySQL语法
            "SELECT * FROM `users` LIMIT 10, 20",
            
            // 包含@rowindex的复杂SQL
            "SELECT @rowindex := @rowindex + 1 AS rn, name FROM users, (SELECT @rowindex := 0) r",
            
            // 包含初始化语句的SQL
            "SELECT data FROM table grades,(Select @rowindex:=-1) b ORDER BY data",
            
            // 复杂的嵌套SQL
            "select scoreMedian1 from(SELECT AVG(g.score1) scoreMedian1 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 FROM(select score1 from table1) grades, (Select @rowindex:=-1) b ORDER BY grades.score1) g)",
            
            // MySQL函数
            "SELECT NOW(), IFNULL(name, 'unknown'), CONCAT(first, last) FROM users",
            
            // 字段名保护测试
            "SELECT tmp1.orgId, t.fromDate FROM table1 tmp1, table2 t"
        };
        
        try {
            StringBuilder result = new StringBuilder();
            result.append("统一SQL转换器测试:\n\n");
            
            for (int i = 0; i < testSqls.length; i++) {
                String sql = testSqls[i];
                result.append("=== 测试 ").append(i + 1).append(" ===\n");
                result.append("原始SQL: ").append(sql).append("\n");
                
                // 使用统一转换器
                String converted = UnifiedSqlConverter.convertSql(sql);
                result.append("转换结果: ").append(converted).append("\n");
                
                // 检查转换效果
                boolean hasIssues = false;
                
                if (converted.contains("@rowindex")) {
                    result.append("⚠️ 警告: 仍包含@rowindex\n");
                    hasIssues = true;
                }
                
                if (converted.contains("(SELECT 1 := -1)") || converted.contains("(SELECT 1 := - 1)")) {
                    result.append("❌ 问题: 发现错误的初始化残留\n");
                    hasIssues = true;
                }
                
                if (converted.contains("SELECTROW_NUMBER")) {
                    result.append("❌ 问题: 发现关键字连接\n");
                    hasIssues = true;
                }
                
                if (converted.contains("OR gId") || converted.contains("FROM Date")) {
                    result.append("❌ 问题: 字段名被破坏\n");
                    hasIssues = true;
                }
                
                if (!hasIssues) {
                    result.append("✅ 转换成功\n");
                }
                
                result.append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("❌ 统一SQL转换器测试失败: {}", e.getMessage(), e);
            return "统一SQL转换器测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试字段名保护
     */
    @GetMapping("/test-field-protection")
    public String testFieldProtection() {
        log.info("🔍 测试字段名保护...");
        
        String[] fieldTests = {
            "SELECT tmp1.orgId FROM table tmp1",
            "SELECT a_or.idf FROM table",
            "SELECT t.fromDate, a.orderBy FROM table t, table2 a",
            "SELECT deptb AS b FROM table"
        };
        
        try {
            StringBuilder result = new StringBuilder();
            result.append("字段名保护测试:\n\n");
            
            for (int i = 0; i < fieldTests.length; i++) {
                String sql = fieldTests[i];
                result.append("测试 ").append(i + 1).append(": ").append(sql).append("\n");
                
                String converted = UnifiedSqlConverter.convertSql(sql);
                result.append("转换: ").append(converted).append("\n");
                
                // 检查字段名是否保持完整
                String[] fieldsToCheck = {"orgId", "idf", "fromDate", "orderBy", "deptb"};
                boolean allFieldsPreserved = true;
                
                for (String field : fieldsToCheck) {
                    if (sql.contains(field) && !converted.contains(field)) {
                        result.append("❌ 问题: 字段 ").append(field).append(" 丢失\n");
                        allFieldsPreserved = false;
                    }
                }
                
                if (allFieldsPreserved) {
                    result.append("✅ 字段名保护成功\n");
                }
                
                result.append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("❌ 字段名保护测试失败: {}", e.getMessage(), e);
            return "字段名保护测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试初始化语句移除
     */
    @GetMapping("/test-initialization-removal")
    public String testInitializationRemoval() {
        log.info("🔧 测试初始化语句移除...");
        
        String[] initTests = {
            "grades,(Select @rowindex:=-1) b",
            "grades, (Select @rowindex:=-1) result",
            "grades,(SELECT @rowindex:=-1) counter",
            "grades,(select @rowindex:=-1) init",
            "FROM table grades,(Select @rowindex:=-1) b  ORDER BY"
        };
        
        try {
            StringBuilder result = new StringBuilder();
            result.append("初始化语句移除测试:\n\n");
            
            for (int i = 0; i < initTests.length; i++) {
                String sql = initTests[i];
                result.append("测试 ").append(i + 1).append(": ").append(sql).append("\n");
                
                String converted = UnifiedSqlConverter.convertSql(sql);
                result.append("转换: ").append(converted).append("\n");
                
                boolean success = !converted.contains("@rowindex") && 
                                 !converted.contains("(Select @rowindex:=-1)") &&
                                 !converted.contains("(SELECT @rowindex:=-1)") &&
                                 !converted.contains("(SELECT 1 := -1)");
                
                if (success) {
                    result.append("✅ 初始化语句移除成功\n");
                } else {
                    result.append("❌ 初始化语句移除失败\n");
                }
                
                result.append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("❌ 初始化语句移除测试失败: {}", e.getMessage(), e);
            return "初始化语句移除测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试MySQL函数转换
     */
    @GetMapping("/test-mysql-functions")
    public String testMysqlFunctions() {
        log.info("🔄 测试MySQL函数转换...");
        
        String[] functionTests = {
            "SELECT NOW() FROM dual",
            "SELECT IFNULL(name, 'unknown') FROM users",
            "SELECT CONCAT(first, last) FROM users",
            "SELECT POW(2, 3) FROM dual",
            "SELECT CURDATE(), CURTIME() FROM dual"
        };
        
        try {
            StringBuilder result = new StringBuilder();
            result.append("MySQL函数转换测试:\n\n");
            
            for (int i = 0; i < functionTests.length; i++) {
                String sql = functionTests[i];
                result.append("测试 ").append(i + 1).append(": ").append(sql).append("\n");
                
                String converted = UnifiedSqlConverter.convertSql(sql);
                result.append("转换: ").append(converted).append("\n");
                
                // 检查函数转换
                boolean hasConversion = false;
                if (sql.contains("NOW()") && converted.contains("SYSDATE")) {
                    result.append("✅ NOW() -> SYSDATE\n");
                    hasConversion = true;
                }
                if (sql.contains("IFNULL") && converted.contains("NVL")) {
                    result.append("✅ IFNULL -> NVL\n");
                    hasConversion = true;
                }
                if (sql.contains("POW") && converted.contains("POWER")) {
                    result.append("✅ POW -> POWER\n");
                    hasConversion = true;
                }
                
                if (!hasConversion && !sql.equals(converted)) {
                    result.append("ℹ️ 其他转换\n");
                } else if (!hasConversion) {
                    result.append("ℹ️ 无需转换\n");
                }
                
                result.append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("❌ MySQL函数转换测试失败: {}", e.getMessage(), e);
            return "MySQL函数转换测试失败: " + e.getMessage();
        }
    }

    /**
     * 综合测试
     */
    @GetMapping("/test-comprehensive")
    public String testComprehensive() {
        log.info("🎯 综合测试...");
        
        // 使用之前testComplexSql中的复杂SQL
        String complexSql = "select scoreMedian1, scoreMedian2, scoreMedian3 from(" +
                "SELECT AVG(g.score1) scoreMedian1 " +
                "FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 " +
                "FROM(select score1 from(select sum(NVL(tt1.total, 0)) as score1 from table1 tt1) grades) grades, " +
                "(Select @rowindex:=-1) b " +
                "ORDER BY grades.score1) g), " +
                "(select AVG(g.score2) scoreMedian2 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score2 AS score2 FROM(select score2 from(select sum(NVL(tt2.total, 0)) as score2 from table2 tt2) grades) grades, (Select @rowindex:=-1) b ORDER BY grades.score2) g), " +
                "(select AVG(g.score3) scoreMedian3 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score3 AS score3 FROM(select score3 from(select sum(NVL(tt3.total, 0)) as score3 from table3 tt3) grades) grades, (Select @rowindex:=-1) b ORDER BY grades.score3) g)";
        
        try {
            StringBuilder result = new StringBuilder();
            result.append("综合测试 - 复杂SQL转换:\n\n");
            result.append("原始SQL长度: ").append(complexSql.length()).append("\n");
            result.append("原始SQL前100字符: ").append(complexSql.substring(0, Math.min(100, complexSql.length()))).append("...\n\n");
            
            // 统计问题模式
            int rowindexCount = countOccurrences(complexSql, "@rowindex");
            int initCount = countOccurrences(complexSql, "(Select @rowindex:=-1) b");
            
            result.append("转换前统计:\n");
            result.append("- @rowindex出现次数: ").append(rowindexCount).append("\n");
            result.append("- 初始化语句次数: ").append(initCount).append("\n\n");
            
            // 使用统一转换器
            String converted = UnifiedSqlConverter.convertSql(complexSql);
            
            result.append("转换后长度: ").append(converted.length()).append("\n");
            result.append("转换后前100字符: ").append(converted.substring(0, Math.min(100, converted.length()))).append("...\n\n");
            
            // 检查转换结果
            int remainingRowindex = countOccurrences(converted, "@rowindex");
            int remainingInit = countOccurrences(converted, "(Select @rowindex:=-1)");
            int errorSyntax = countOccurrences(converted, "(SELECT 1 := -1)") + 
                             countOccurrences(converted, "(SELECT 1 := - 1)");
            int rowtNumberCount = countOccurrences(converted, "ROW_NUMBER");
            
            result.append("转换后统计:\n");
            result.append("- 剩余@rowindex: ").append(remainingRowindex).append("\n");
            result.append("- 剩余初始化语句: ").append(remainingInit).append("\n");
            result.append("- 错误语法: ").append(errorSyntax).append("\n");
            result.append("- ROW_NUMBER数量: ").append(rowtNumberCount).append("\n\n");
            
            // 总体评估
            boolean success = remainingRowindex == 0 && remainingInit == 0 && errorSyntax == 0 && rowtNumberCount > 0;
            
            if (success) {
                result.append("🎉 综合测试成功！\n");
                result.append("✅ 所有@rowindex已转换为ROW_NUMBER()\n");
                result.append("✅ 所有初始化语句已移除\n");
                result.append("✅ 没有产生错误语法\n");
            } else {
                result.append("⚠️ 综合测试发现问题:\n");
                if (remainingRowindex > 0) result.append("- 仍有@rowindex残留\n");
                if (remainingInit > 0) result.append("- 仍有初始化语句\n");
                if (errorSyntax > 0) result.append("- 产生了错误语法\n");
                if (rowtNumberCount == 0) result.append("- 没有生成ROW_NUMBER()\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("❌ 综合测试失败: {}", e.getMessage(), e);
            return "综合测试失败: " + e.getMessage();
        }
    }

    /**
     * 计算字符串出现次数
     */
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
