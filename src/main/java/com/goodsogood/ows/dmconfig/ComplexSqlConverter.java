package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.HashMap;
import java.util.Map;

/**
 * 复杂SQL转换器
 * 专门处理复杂的MySQL语法转换为达梦兼容语法
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Log4j2
public class ComplexSqlConverter {

    // MySQL变量模式 - 考虑各种空格、换行、注释情况
    private static final Pattern MYSQL_VAR_INIT_PATTERN = Pattern.compile(
        ",\\s*\\(\\s*SELECT\\s+(@\\w+)\\s*:?\\s*=\\s*(\\d+)\\s*\\)\\s*(\\w*)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    private static final Pattern MYSQL_VAR_INCREMENT_PATTERN = Pattern.compile(
        "(@\\w+)\\s*:?\\s*=\\s*\\1\\s*\\+\\s*(\\d+)\\s+AS\\s+(\\w+)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    private static final Pattern MYSQL_VAR_ASSIGNMENT_PATTERN = Pattern.compile(
        "(@\\w+)\\s*:?\\s*=\\s*([^,\\s]+)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // 更复杂的变量模式 - 处理换行和注释
    private static final Pattern MYSQL_VAR_COMPLEX_PATTERN = Pattern.compile(
        "(@\\w+)\\s*(?:/\\*.*?\\*/)?\\s*:?\\s*(?:/\\*.*?\\*/)?\\s*=\\s*(?:/\\*.*?\\*/)?\\s*\\1\\s*(?:/\\*.*?\\*/)?\\s*\\+\\s*(?:/\\*.*?\\*/)?\\s*(\\d+)\\s*(?:/\\*.*?\\*/)?\\s+AS\\s+(?:/\\*.*?\\*/)?\\s*(\\w+)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // 处理各种空格和特殊字符的模式
    private static final Pattern WHITESPACE_NORMALIZE_PATTERN = Pattern.compile(
        "\\s+", Pattern.MULTILINE
    );

    // MySQL注释模式
    private static final Pattern MYSQL_COMMENT_PATTERN = Pattern.compile(
        "(/\\*.*?\\*/|--.*?(?=\\n|$)|#.*?(?=\\n|$))",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // 函数转换模式 - 考虑空格、换行、注释
    private static final Pattern IFNULL_PATTERN = Pattern.compile(
        "\\bIFNULL\\s*\\(\\s*([^,]+?)\\s*,\\s*([^)]+?)\\s*\\)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    private static final Pattern UNIX_TIMESTAMP_PATTERN = Pattern.compile(
        "\\bUNIX_TIMESTAMP\\s*\\(\\s*([^)]+?)\\s*\\)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    private static final Pattern DATE_FORMAT_PATTERN = Pattern.compile(
        "\\bDATE_FORMAT\\s*\\(\\s*([^,]+?)\\s*,\\s*'([^']+?)'\\s*\\)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    private static final Pattern CONCAT_WS_PATTERN = Pattern.compile(
        "\\bCONCAT_WS\\s*\\(\\s*([^,]+?)\\s*,\\s*([^)]+?)\\s*\\)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    private static final Pattern GROUP_CONCAT_PATTERN = Pattern.compile(
        "\\bGROUP_CONCAT\\s*\\(\\s*([^)]+?)\\s*\\)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // 更复杂的函数模式 - 处理嵌套括号
    private static final Pattern NESTED_FUNCTION_PATTERN = Pattern.compile(
        "\\b(\\w+)\\s*\\(([^()]*(?:\\([^()]*\\)[^()]*)*)\\)",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    // 子查询和窗口函数模式
    private static final Pattern SUBQUERY_LIMIT_PATTERN = Pattern.compile(
        "\\(\\s*SELECT\\s+[^)]+\\s+LIMIT\\s+(\\d+)\\s*\\)",
        Pattern.CASE_INSENSITIVE
    );

    // 数据类型转换模式
    private static final Pattern MYSQL_CAST_PATTERN = Pattern.compile(
        "\\bCAST\\s*\\(([^)]+)\\s+AS\\s+(SIGNED|UNSIGNED|DECIMAL|CHAR|DATE|DATETIME|TIME)\\)",
        Pattern.CASE_INSENSITIVE
    );

    // 字符串函数模式
    private static final Pattern SUBSTRING_INDEX_PATTERN = Pattern.compile(
        "\\bSUBSTRING_INDEX\\s*\\(([^,]+),([^,]+),([^)]+)\\)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern FIND_IN_SET_PATTERN = Pattern.compile(
        "\\bFIND_IN_SET\\s*\\(([^,]+),([^)]+)\\)",
        Pattern.CASE_INSENSITIVE
    );

    /**
     * 转换包含MySQL变量的复杂SQL
     *
     * @param sql 原始SQL
     * @return 转换后的SQL
     */
    public static String convertComplexSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        String convertedSql = sql;

        try {
            log.info("开始复杂SQL转换...");
            log.info("原始SQL: {}", sql);

            // 0. 预处理 - 标准化空格和格式
            convertedSql = preprocessSql(convertedSql);

            // 1. 先处理MySQL变量初始化
            convertedSql = convertMysqlVariables(convertedSql);

            // 2. 处理 @rowindex 变量模式
            convertedSql = convertRowIndexPattern(convertedSql);

            // 3. 处理嵌套查询中的变量
            convertedSql = convertNestedQueryVariables(convertedSql);

            // 4. 处理MySQL特有函数
            convertedSql = convertMysqlFunctions(convertedSql);

            // 5. 处理子查询和窗口函数
            convertedSql = convertSubqueriesAndWindows(convertedSql);

            // 6. 处理数据类型转换
            convertedSql = convertDataTypes(convertedSql);

            // 7. 处理字符串函数
            convertedSql = convertStringFunctions(convertedSql);

            // 8. 处理特殊语法结构
            convertedSql = convertSpecialSyntax(convertedSql);

            // 9. 处理分析函数
            convertedSql = convertAnalyticFunctions(convertedSql);

            // 10. 处理JSON函数
            convertedSql = convertJsonFunctions(convertedSql);

            // 11. 处理正则表达式函数
            convertedSql = convertRegexFunctions(convertedSql);

            // 12. 处理数学函数
            convertedSql = convertMathFunctions(convertedSql);

            // 13. 最后清理和优化
            convertedSql = cleanupSql(convertedSql);

            log.info("复杂SQL转换完成: {}", convertedSql);

        } catch (Exception e) {
            log.warn("复杂SQL转换失败，使用原始SQL: {}", e.getMessage());
            return sql;
        }

        return convertedSql;
    }

    /**
     * 清理和优化SQL
     */
    private static String cleanupSql(String sql) {
        // 先确保关键字之间有适当的空格 - 使用更安全的方法
        // 处理SELECT和ROW_NUMBER连接的问题
        sql = sql.replaceAll("(?i)SELECT\\s*ROW_NUMBER", "SELECT ROW_NUMBER");
        sql = sql.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");

        // 处理ROW_NUMBER和OVER连接的问题
        sql = sql.replaceAll("(?i)ROW_NUMBER\\s*OVER", "ROW_NUMBER() OVER");
        sql = sql.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");

        // 确保ROW_NUMBER后面有括号
        sql = sql.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");

        // 处理OVER和括号的问题
        sql = sql.replaceAll("(?i)OVER\\s*\\(", "OVER(");
        sql = sql.replaceAll("(?i)OVER(?!\\s*\\()", "OVER(ORDER BY 1)");

        // 清理多余的空格
        sql = sql.replaceAll("\\s+", " ");

        // 清理多余的逗号
        sql = sql.replaceAll(",\\s*,", ",");
        sql = sql.replaceAll(",\\s*FROM", " FROM");
        sql = sql.replaceAll(",\\s*WHERE", " WHERE");

        return sql.trim();
    }

    /**
     * SQL预处理 - 标准化空格、换行、注释等
     */
    private static String preprocessSql(String sql) {
        log.debug("开始SQL预处理...");

        String processed = sql;

        // 1. 保护字符串字面量（避免在字符串内部进行替换）
        Map<String, String> stringLiterals = new HashMap<>();
        int literalCounter = 0;

        // 提取单引号字符串
        Pattern singleQuotePattern = Pattern.compile("'([^'\\\\]|\\\\.)*'");
        Matcher singleQuoteMatcher = singleQuotePattern.matcher(processed);
        while (singleQuoteMatcher.find()) {
            String placeholder = "___STRING_LITERAL_" + (literalCounter++) + "___";
            stringLiterals.put(placeholder, singleQuoteMatcher.group());
            processed = processed.replace(singleQuoteMatcher.group(), placeholder);
        }

        // 提取双引号字符串
        Pattern doubleQuotePattern = Pattern.compile("\"([^\"\\\\]|\\\\.)*\"");
        Matcher doubleQuoteMatcher = doubleQuotePattern.matcher(processed);
        while (doubleQuoteMatcher.find()) {
            String placeholder = "___STRING_LITERAL_" + (literalCounter++) + "___";
            stringLiterals.put(placeholder, doubleQuoteMatcher.group());
            processed = processed.replace(doubleQuoteMatcher.group(), placeholder);
        }

        // 2. 处理注释
        processed = handleComments(processed);

        // 3. 标准化空格
        processed = normalizeWhitespace(processed);

        // 4. 处理特殊的MySQL语法变体
        processed = handleMysqlSyntaxVariants(processed);

        // 5. 恢复字符串字面量
        for (Map.Entry<String, String> entry : stringLiterals.entrySet()) {
            processed = processed.replace(entry.getKey(), entry.getValue());
        }

        log.debug("SQL预处理完成");
        return processed;
    }

    /**
     * 处理注释
     */
    private static String handleComments(String sql) {
        // 移除单行注释 (-- 和 #)
        sql = sql.replaceAll("--[^\\r\\n]*", "");
        sql = sql.replaceAll("#[^\\r\\n]*", "");

        // 保留多行注释中的重要信息，但简化格式
        sql = sql.replaceAll("/\\*\\s*([^*]|\\*(?!/))*\\s*\\*/", " ");

        return sql;
    }

    /**
     * 标准化空格
     */
    private static String normalizeWhitespace(String sql) {
        // 将所有类型的空白字符（空格、制表符、换行符等）标准化为单个空格
        sql = sql.replaceAll("\\s+", " ");

        // 处理操作符周围的空格
        sql = sql.replaceAll("\\s*:\\s*=\\s*", " := ");
        sql = sql.replaceAll("\\s*:=\\s*", " := ");
        sql = sql.replaceAll("\\s*=\\s*", " = ");
        sql = sql.replaceAll("\\s*\\+\\s*", " + ");
        sql = sql.replaceAll("\\s*-\\s*", " - ");
        sql = sql.replaceAll("\\s*\\*\\s*", " * ");
        sql = sql.replaceAll("\\s*/\\s*", " / ");

        // 处理括号周围的空格
        sql = sql.replaceAll("\\s*\\(\\s*", " (");
        sql = sql.replaceAll("\\s*\\)\\s*", ") ");
        sql = sql.replaceAll("\\s*,\\s*", ", ");

        return sql.trim();
    }

    /**
     * 处理MySQL语法变体
     */
    private static String handleMysqlSyntaxVariants(String sql) {
        // 处理各种 := 的写法变体
        sql = sql.replaceAll("\\s*:\\s*=\\s*", " := ");
        sql = sql.replaceAll("\\s*:=\\s*", " := ");

        // 处理 SELECT 后面可能的空格变体
        sql = sql.replaceAll("(?i)SELECT\\s+", "SELECT ");
        sql = sql.replaceAll("(?i)FROM\\s+", "FROM ");
        sql = sql.replaceAll("(?i)WHERE\\s+", "WHERE ");
        sql = sql.replaceAll("(?i)ORDER\\s+BY\\s+", "ORDER BY ");
        sql = sql.replaceAll("(?i)GROUP\\s+BY\\s+", "GROUP BY ");

        return sql;
    }

    /**
     * 转换 @rowindex 模式
     * 将 SELECT @rowindex := @rowindex + 1 AS rowindex 转换为 ROW_NUMBER() OVER() AS rowindex
     */
    private static String convertRowIndexPattern(String sql) {
        // 模式1: SELECT @rowindex := @rowindex + 1 AS rowindex
        Pattern pattern1 = Pattern.compile(
            "SELECT\\s+@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
            Pattern.CASE_INSENSITIVE
        );
        sql = pattern1.matcher(sql).replaceAll("SELECT ROW_NUMBER() OVER(ORDER BY 1) AS $1");

        // 模式2: @rowindex := @rowindex + 1 AS rowindex (在SELECT子句中)
        Pattern pattern2 = Pattern.compile(
            "@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
            Pattern.CASE_INSENSITIVE
        );
        sql = pattern2.matcher(sql).replaceAll("ROW_NUMBER() OVER(ORDER BY 1) AS $1");

        // 模式3: 处理更复杂的情况，包含ORDER BY的
        Pattern pattern3 = Pattern.compile(
            "(@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+\\w+)([^)]*ORDER\\s+BY\\s+[^)]+)",
            Pattern.CASE_INSENSITIVE
        );
        Matcher matcher3 = pattern3.matcher(sql);
        if (matcher3.find()) {
            String orderByClause = matcher3.group(2);
            sql = matcher3.replaceAll("ROW_NUMBER() OVER(" + orderByClause + ") AS rowindex");
        }

        return sql;
    }

    /**
     * 转换MySQL变量
     */
    private static String convertMysqlVariables(String sql) {
        log.info("开始转换MySQL变量...");

        // 移除变量初始化 (SELECT @rowindex := 0) r
        Pattern initPattern = Pattern.compile(
            ",\\s*\\(\\s*SELECT\\s+@\\w+\\s*:?=\\s*\\d+\\s*\\)\\s*\\w*",
            Pattern.CASE_INSENSITIVE
        );
        sql = initPattern.matcher(sql).replaceAll("");

        // 清理多余的逗号
        sql = sql.replaceAll(",\\s*,", ",");
        sql = sql.replaceAll(",\\s*FROM", " FROM");

        log.info("MySQL变量转换完成");
        return sql;
    }

    /**
     * 处理嵌套查询中的变量
     */
    private static String convertNestedQueryVariables(String sql) {
        // 如果SQL包含复杂的嵌套结构，可能需要重写为窗口函数
        if (sql.toLowerCase().contains("@rowindex") && sql.toLowerCase().contains("from(")) {
            // 尝试识别并转换复杂的嵌套查询
            sql = convertNestedRowNumberQuery(sql);
        }
        
        return sql;
    }

    /**
     * 转换复杂的嵌套ROW_NUMBER查询
     */
    private static String convertNestedRowNumberQuery(String sql) {
        // 这里处理类似这样的复杂查询:
        // SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1
        // FROM (select score1 from ...) grades, (SELECT @rowindex := 0) r

        // 简化处理：如果发现这种模式，尝试用ROW_NUMBER()替换
        if (sql.contains("@rowindex")) {
            // 移除变量初始化部分 (SELECT @rowindex := 0) r
            sql = sql.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@\\w+\\s*:?=\\s*\\d+\\s*\\)\\s*\\w*", "");

            // 替换变量为ROW_NUMBER()，保持ORDER BY子句
            if (sql.toLowerCase().contains("order by")) {
                // 提取ORDER BY子句
                Pattern orderByPattern = Pattern.compile("ORDER\\s+BY\\s+([^)]+)", Pattern.CASE_INSENSITIVE);
                Matcher orderByMatcher = orderByPattern.matcher(sql);
                if (orderByMatcher.find()) {
                    String orderByClause = orderByMatcher.group(1);
                    sql = sql.replaceAll("(?i)@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1",
                                       "ROW_NUMBER() OVER(ORDER BY " + orderByClause + ")");
                } else {
                    sql = sql.replaceAll("(?i)@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1",
                                       "ROW_NUMBER() OVER(ORDER BY 1)");
                }
            } else {
                sql = sql.replaceAll("(?i)@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1",
                                   "ROW_NUMBER() OVER(ORDER BY 1)");
            }

            // 清理多余的逗号和空格
            sql = sql.replaceAll(",\\s*FROM", " FROM");
            sql = sql.replaceAll("\\s+", " ").trim();
        }

        return sql;
    }

    /**
     * 检查是否是复杂SQL（包含MySQL特有语法）
     */
    public static boolean isComplexSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String lowerSql = sql.toLowerCase();

        return lowerSql.contains("@rowindex") ||
               lowerSql.contains("@") ||
               (lowerSql.contains("select") && lowerSql.contains(":=")) ||
               lowerSql.contains("mysql") ||
               // MySQL函数
               lowerSql.contains("ifnull") ||
               lowerSql.contains("unix_timestamp") ||
               lowerSql.contains("date_format") ||
               lowerSql.contains("concat_ws") ||
               lowerSql.contains("group_concat") ||
               lowerSql.contains("substring_index") ||
               lowerSql.contains("find_in_set") ||
               // MySQL特殊语法
               lowerSql.contains("on duplicate key") ||
               lowerSql.contains("replace into") ||
               lowerSql.contains("straight_join") ||
               lowerSql.contains("force index") ||
               lowerSql.contains("use index") ||
               lowerSql.contains("ignore index") ||
               lowerSql.contains("sql_calc_found_rows") ||
               lowerSql.contains("found_rows()") ||
               lowerSql.contains("last_insert_id()") ||
               lowerSql.contains("connection_id()") ||
               // JSON函数
               lowerSql.contains("json_extract") ||
               lowerSql.contains("json_unquote") ||
               lowerSql.contains("json_object") ||
               lowerSql.contains("json_array") ||
               // 正则表达式
               lowerSql.contains(" regexp ") ||
               lowerSql.contains(" rlike ") ||
               lowerSql.contains("regexp_replace") ||
               // 数学函数
               lowerSql.contains("pow(") ||
               lowerSql.contains("log(") ||
               lowerSql.contains("log10(") ||
               lowerSql.contains("rand()") ||
               // 其他复杂情况
               lowerSql.contains("cast(") && (lowerSql.contains("signed") || lowerSql.contains("unsigned"));
    }

    /**
     * 智能SQL转换
     * 根据SQL复杂度选择合适的转换策略
     */
    public static String smartConvert(String sql) {
        if (isComplexSql(sql)) {
            log.info("检测到复杂SQL，使用复杂转换器");

            // 使用模式检测器分析SQL
            SqlPatternDetector.SqlPatternAnalysis analysis = SqlPatternDetector.analyzeSql(sql);
            log.info("SQL模式分析: 复杂度={}, 策略={}", analysis.complexityScore, analysis.conversionStrategy);

            // 根据分析结果选择转换策略
            switch (analysis.conversionStrategy) {
                case "COMPLEX_VARIABLE_CONVERSION":
                    if (sql.toLowerCase().contains("@rowindex")) {
                        return convertRowIndexSql(sql);
                    } else {
                        return convertComplexSql(sql);
                    }
                case "FUNCTION_CONVERSION":
                case "NESTED_QUERY_CONVERSION":
                    return convertComplexSql(sql);
                default:
                    return convertComplexSql(sql);
            }
        } else {
            return SqlCompatibilityUtil.convertSql(sql);
        }
    }

    /**
     * 专门转换包含@rowindex的SQL
     */
    public static String convertRowIndexSql(String sql) {
        log.info("🔄 开始转换@rowindex SQL...");

        String result = sql;

        // 步骤0: 预处理SQL
        result = preprocessSql(result);
        log.info("步骤0 - 预处理完成: {}", result);

        // 步骤1: 移除变量初始化部分 - 处理各种复杂情况
        String[] initPatterns = {
            // 带逗号的模式
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*0\\s*\\)\\s*\\w*",
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*0\\s*\\)\\s*\\w*",
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*=\\s*0\\s*\\)\\s*\\w*",
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:\\s*=\\s*0\\s*\\)\\s*\\w*",
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*",
            "(?i),\\s*\\(\\s*SELECT\\s+@\\w+\\s*:?\\s*=\\s*-?\\d+\\s*\\)\\s*\\w*",

            // 不带逗号的模式
            "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*0\\s*\\)\\s*\\w*",
            "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*0\\s*\\)\\s*\\w*",
            "(?i)\\(\\s*SELECT\\s+@rowindex\\s*=\\s*0\\s*\\)\\s*\\w*",
            "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:\\s*=\\s*0\\s*\\)\\s*\\w*",
            "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*",
            "(?i)\\(\\s*SELECT\\s+@\\w+\\s*:?\\s*=\\s*-?\\d+\\s*\\)\\s*\\w*"
        };

        for (String pattern : initPatterns) {
            String before = result;
            result = result.replaceAll(pattern, "");
            if (!before.equals(result)) {
                log.debug("移除初始化语句，使用模式: {}", pattern);
            }
        }
        log.info("步骤1 - 移除变量初始化: {}", result);

        // 步骤2: 替换@rowindex变量为ROW_NUMBER() - 处理各种变体
        String[] rowIndexPatterns = {
            "(?i)@rowindex\\s*:=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
            "(?i)@rowindex\\s*:\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
            "(?i)@rowindex\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
            "(?i)@rowindex\\s*:=\\s*@rowindex\\s*\\+\\s*1\\s*AS\\s+(\\w+)",
            "(?i)@rowindex\\s*:\\s*=\\s*@rowindex\\s*\\+\\s*1\\s*AS\\s+(\\w+)"
        };

        boolean replaced = false;
        for (String pattern : rowIndexPatterns) {
            if (result.matches(".*" + pattern + ".*")) {
                result = result.replaceAll(pattern, "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
                replaced = true;
                break;
            }
        }

        if (!replaced && result.toLowerCase().contains("@rowindex")) {
            // 最宽松的匹配
            result = result.replaceAll("(?i)@rowindex[^,\\s]*", "ROW_NUMBER() OVER(ORDER BY 1)");
        }

        log.info("步骤2 - 替换@rowindex: {}", result);

        // 步骤2.1: 处理残留的@rowindex
        int maxAttempts = 3;
        int attempts = 0;
        while (result.toLowerCase().contains("@rowindex") && attempts < maxAttempts) {
            attempts++;
            log.info("检测到@rowindex残留，进行第{}次清理...", attempts);

            // 逐步精确的清理策略，避免误匹配初始化语句
            switch (attempts) {
                case 1:
                    // 只匹配明确的@rowindex := @rowindex + 1模式，必须包含两个@rowindex和+号
                    result = result.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
                    result = result.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1(?=\\s|,|$|\\))", "ROW_NUMBER() OVER(ORDER BY 1)");
                    break;
                case 2:
                    // 更精确的残留清理，必须是完整的递增模式
                    result = result.replaceAll("(?i)@rowindex(?=\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1)", "ROW_NUMBER() OVER(ORDER BY 1)");
                    break;
                case 3:
                    // 最后的兜底清理，只替换不是初始化的@rowindex
                    // 改进负向前瞻，包含负数的情况
                    result = result.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
                    log.warn("发现无法精确转换的@rowindex，已替换为常量1");
                    break;
            }

            log.info("第{}次清理后: {}", attempts, result);
        }

        // 步骤3: 清理多余的逗号和空格
        result = cleanupSqlSyntax(result);
        log.info("步骤3 - 语法清理完成: {}", result);

        // 步骤4: 应用其他基础转换
        result = SqlCompatibilityUtil.convertSql(result);
        log.info("步骤4 - 基础转换完成: {}", result);

        // 步骤5: 最终检查和警告
        if (result.contains("@")) {
            log.warn("⚠️ 转换后仍包含@符号，可能需要手动处理: {}", result);
            // 尝试最后的清理
            result = result.replaceAll("@\\w+", "1");
            log.info("最终清理后: {}", result);
        }

        log.info("✅ @rowindex SQL转换完成");
        return result;
    }

    /**
     * 清理SQL语法
     */
    private static String cleanupSqlSyntax(String sql) {
        // 先确保关键字之间有适当的空格 - 使用更安全的方法
        // 处理SELECT和ROW_NUMBER连接的问题
        sql = sql.replaceAll("(?i)SELECT\\s*ROW_NUMBER", "SELECT ROW_NUMBER");
        sql = sql.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");

        // 处理ROW_NUMBER和OVER连接的问题
        sql = sql.replaceAll("(?i)ROW_NUMBER\\s*OVER", "ROW_NUMBER() OVER");
        sql = sql.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");

        // 确保ROW_NUMBER后面有括号
        sql = sql.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");

        // 处理OVER和括号的问题
        sql = sql.replaceAll("(?i)OVER\\s*\\(", "OVER(");
        sql = sql.replaceAll("(?i)OVER(?!\\s*\\()", "OVER(ORDER BY 1)");

        // 清理多余的逗号
        sql = sql.replaceAll(",\\s*,", ",");
        sql = sql.replaceAll(",\\s*FROM", " FROM");
        sql = sql.replaceAll(",\\s*WHERE", " WHERE");
        sql = sql.replaceAll(",\\s*ORDER", " ORDER");
        sql = sql.replaceAll(",\\s*GROUP", " GROUP");
        sql = sql.replaceAll(",\\s*HAVING", " HAVING");
        sql = sql.replaceAll(",\\s*LIMIT", " LIMIT");

        // 清理多余的空格
        sql = sql.replaceAll("\\s+", " ");

        // 清理多余的括号
        sql = sql.replaceAll("\\(\\s*\\)", "");

        return sql.trim();
    }

    /**
     * 转换MySQL特有函数
     */
    private static String convertMysqlFunctions(String sql) {
        log.debug("转换MySQL函数...");

        // 1. IFNULL -> NVL
        sql = IFNULL_PATTERN.matcher(sql).replaceAll("NVL($1,$2)");

        // 2. UNIX_TIMESTAMP -> EXTRACT(EPOCH FROM timestamp)
        sql = UNIX_TIMESTAMP_PATTERN.matcher(sql).replaceAll("EXTRACT(EPOCH FROM $1)");

        // 3. DATE_FORMAT -> TO_CHAR
        Matcher dateFormatMatcher = DATE_FORMAT_PATTERN.matcher(sql);
        while (dateFormatMatcher.find()) {
            String dateExpr = dateFormatMatcher.group(1);
            String format = dateFormatMatcher.group(2);
            String dmFormat = convertDateFormat(format);
            sql = dateFormatMatcher.replaceFirst("TO_CHAR(" + dateExpr + ", '" + dmFormat + "')");
            dateFormatMatcher = DATE_FORMAT_PATTERN.matcher(sql);
        }

        // 4. CONCAT_WS -> 自定义实现
        sql = CONCAT_WS_PATTERN.matcher(sql).replaceAll("CONCAT_WS_DM($1,$2)");

        // 5. GROUP_CONCAT -> LISTAGG
        sql = GROUP_CONCAT_PATTERN.matcher(sql).replaceAll("LISTAGG($1, ',') WITHIN GROUP (ORDER BY $1)");

        return sql;
    }

    /**
     * 转换日期格式字符串
     */
    private static String convertDateFormat(String mysqlFormat) {
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put("%Y", "YYYY");
        formatMap.put("%y", "YY");
        formatMap.put("%m", "MM");
        formatMap.put("%d", "DD");
        formatMap.put("%H", "HH24");
        formatMap.put("%h", "HH12");
        formatMap.put("%i", "MI");
        formatMap.put("%s", "SS");
        formatMap.put("%M", "Month");
        formatMap.put("%b", "Mon");
        formatMap.put("%W", "Day");
        formatMap.put("%w", "D");

        String dmFormat = mysqlFormat;
        for (Map.Entry<String, String> entry : formatMap.entrySet()) {
            dmFormat = dmFormat.replace(entry.getKey(), entry.getValue());
        }
        return dmFormat;
    }

    /**
     * 转换子查询和窗口函数
     */
    private static String convertSubqueriesAndWindows(String sql) {
        log.debug("转换子查询和窗口函数...");

        // 处理子查询中的LIMIT
        Matcher limitMatcher = SUBQUERY_LIMIT_PATTERN.matcher(sql);
        while (limitMatcher.find()) {
            String limitValue = limitMatcher.group(1);
            String subquery = limitMatcher.group(0);
            // 将LIMIT转换为ROW_NUMBER()窗口函数
            String converted = subquery.replace("LIMIT " + limitValue,
                ") WHERE ROWNUM <= " + limitValue);
            sql = sql.replace(subquery, converted);
        }

        return sql;
    }

    /**
     * 转换数据类型
     */
    private static String convertDataTypes(String sql) {
        log.debug("转换数据类型...");

        Matcher castMatcher = MYSQL_CAST_PATTERN.matcher(sql);
        while (castMatcher.find()) {
            String expr = castMatcher.group(1);
            String type = castMatcher.group(2).toUpperCase();

            String dmType;
            switch (type) {
                case "SIGNED":
                    dmType = "INTEGER";
                    break;
                case "UNSIGNED":
                    dmType = "INTEGER";
                    break;
                case "DECIMAL":
                    dmType = "DECIMAL";
                    break;
                case "CHAR":
                    dmType = "VARCHAR";
                    break;
                case "DATE":
                    dmType = "DATE";
                    break;
                case "DATETIME":
                    dmType = "TIMESTAMP";
                    break;
                case "TIME":
                    dmType = "TIME";
                    break;
                default:
                    dmType = type;
            }

            sql = castMatcher.replaceFirst("CAST(" + expr + " AS " + dmType + ")");
            castMatcher = MYSQL_CAST_PATTERN.matcher(sql);
        }

        return sql;
    }

    /**
     * 转换字符串函数
     */
    private static String convertStringFunctions(String sql) {
        log.debug("转换字符串函数...");

        // 1. SUBSTRING_INDEX -> 自定义实现
        sql = SUBSTRING_INDEX_PATTERN.matcher(sql).replaceAll("SUBSTRING_INDEX_DM($1,$2,$3)");

        // 2. FIND_IN_SET -> INSTR
        sql = FIND_IN_SET_PATTERN.matcher(sql).replaceAll("CASE WHEN INSTR(',' || $2 || ',', ',' || $1 || ',') > 0 THEN 1 ELSE 0 END");

        return sql;
    }

    /**
     * 转换特殊语法结构
     */
    private static String convertSpecialSyntax(String sql) {
        log.debug("转换特殊语法结构...");

        // 1. 处理MySQL的INSERT ... ON DUPLICATE KEY UPDATE
        sql = sql.replaceAll("(?i)\\bON\\s+DUPLICATE\\s+KEY\\s+UPDATE\\b", "ON CONFLICT DO UPDATE SET");

        // 2. 处理MySQL的REPLACE INTO
        sql = sql.replaceAll("(?i)\\bREPLACE\\s+INTO\\b", "INSERT OR REPLACE INTO");

        // 3. 处理MySQL的STRAIGHT_JOIN
        sql = sql.replaceAll("(?i)\\bSTRAIGHT_JOIN\\b", "JOIN");

        // 4. 处理MySQL的FORCE INDEX
        sql = sql.replaceAll("(?i)\\bFORCE\\s+INDEX\\s*\\([^)]+\\)", "");

        // 5. 处理MySQL的USE INDEX
        sql = sql.replaceAll("(?i)\\bUSE\\s+INDEX\\s*\\([^)]+\\)", "");

        // 6. 处理MySQL的IGNORE INDEX
        sql = sql.replaceAll("(?i)\\bIGNORE\\s+INDEX\\s*\\([^)]+\\)", "");

        // 7. 处理MySQL的SQL_CALC_FOUND_ROWS
        sql = sql.replaceAll("(?i)\\bSQL_CALC_FOUND_ROWS\\b", "");

        // 8. 处理MySQL的FOUND_ROWS()
        sql = sql.replaceAll("(?i)\\bFOUND_ROWS\\s*\\(\\s*\\)", "@@ROWCOUNT");

        // 9. 处理MySQL的LAST_INSERT_ID()
        sql = sql.replaceAll("(?i)\\bLAST_INSERT_ID\\s*\\(\\s*\\)", "SCOPE_IDENTITY()");

        // 10. 处理MySQL的CONNECTION_ID()
        sql = sql.replaceAll("(?i)\\bCONNECTION_ID\\s*\\(\\s*\\)", "@@SPID");

        return sql;
    }

    /**
     * 转换复杂的聚合和分析函数
     */
    private static String convertAnalyticFunctions(String sql) {
        log.debug("转换分析函数...");

        // 1. 处理MySQL的窗口函数语法差异
        // MySQL: RANK() OVER (PARTITION BY col ORDER BY col)
        // 达梦: 基本相同，但可能需要调整语法

        // 2. 处理MySQL的PERCENT_RANK
        sql = sql.replaceAll("(?i)\\bPERCENT_RANK\\s*\\(\\s*\\)", "PERCENT_RANK()");

        // 3. 处理MySQL的CUME_DIST
        sql = sql.replaceAll("(?i)\\bCUME_DIST\\s*\\(\\s*\\)", "CUME_DIST()");

        // 4. 处理MySQL的NTILE
        sql = sql.replaceAll("(?i)\\bNTILE\\s*\\((\\d+)\\)", "NTILE($1)");

        return sql;
    }

    /**
     * 转换JSON函数（MySQL 5.7+）
     */
    private static String convertJsonFunctions(String sql) {
        log.debug("转换JSON函数...");

        // 1. JSON_EXTRACT -> JSON_VALUE
        sql = sql.replaceAll("(?i)\\bJSON_EXTRACT\\s*\\(([^,]+),\\s*'([^']+)'\\)", "JSON_VALUE($1, '$2')");

        // 2. JSON_UNQUOTE -> JSON_VALUE
        sql = sql.replaceAll("(?i)\\bJSON_UNQUOTE\\s*\\(([^)]+)\\)", "JSON_VALUE($1, '$')");

        // 3. JSON_OBJECT -> JSON_OBJECT (达梦可能支持)
        // 保持不变，如果不支持需要手动处理

        // 4. JSON_ARRAY -> JSON_ARRAY (达梦可能支持)
        // 保持不变，如果不支持需要手动处理

        return sql;
    }

    /**
     * 转换正则表达式函数
     */
    private static String convertRegexFunctions(String sql) {
        log.debug("转换正则表达式函数...");

        // 1. REGEXP -> REGEXP_LIKE
        sql = sql.replaceAll("(?i)\\b(\\w+)\\s+REGEXP\\s+'([^']+)'", "REGEXP_LIKE($1, '$2')");

        // 2. RLIKE -> REGEXP_LIKE
        sql = sql.replaceAll("(?i)\\b(\\w+)\\s+RLIKE\\s+'([^']+)'", "REGEXP_LIKE($1, '$2')");

        // 3. REGEXP_REPLACE (MySQL 8.0+)
        sql = sql.replaceAll("(?i)\\bREGEXP_REPLACE\\s*\\(([^,]+),\\s*'([^']+)',\\s*'([^']+)'\\)",
                           "REGEXP_REPLACE($1, '$2', '$3')");

        return sql;
    }

    /**
     * 转换数学函数
     */
    private static String convertMathFunctions(String sql) {
        log.debug("转换数学函数...");

        // 1. POW -> POWER
        sql = sql.replaceAll("(?i)\\bPOW\\s*\\(", "POWER(");

        // 2. LOG -> LN (自然对数)
        sql = sql.replaceAll("(?i)\\bLOG\\s*\\(([^,)]+)\\)", "LN($1)");

        // 3. LOG10 -> LOG (以10为底)
        sql = sql.replaceAll("(?i)\\bLOG10\\s*\\(", "LOG(10, ");

        // 4. RAND -> RANDOM
        sql = sql.replaceAll("(?i)\\bRAND\\s*\\(\\s*\\)", "RANDOM()");

        return sql;
    }
}
