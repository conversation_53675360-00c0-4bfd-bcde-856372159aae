package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * 全局SqlSessionFactory后处理器
 * 自动为所有SqlSessionFactory添加达梦数据库SQL转换拦截器
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Component
@Log4j2
public class GlobalSqlSessionFactoryPostProcessor implements BeanPostProcessor {

    @Autowired
    private StatementHandlerInterceptor statementHandlerInterceptor;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof SqlSessionFactory) {
            SqlSessionFactory sqlSessionFactory = (SqlSessionFactory) bean;
            
            log.info("🔧 为SqlSessionFactory [{}] 添加达梦数据库SQL转换拦截器", beanName);
            
            // 检查是否已经添加了拦截器
            boolean hasInterceptor = sqlSessionFactory.getConfiguration()
                .getInterceptors()
                .stream()
                .anyMatch(interceptor -> interceptor instanceof StatementHandlerInterceptor);
            
            if (!hasInterceptor) {
                sqlSessionFactory.getConfiguration().addInterceptor(statementHandlerInterceptor);
                log.info("✅ 成功为SqlSessionFactory [{}] 添加拦截器", beanName);
            } else {
                log.info("⚠️ SqlSessionFactory [{}] 已经包含拦截器，跳过", beanName);
            }
            
            // 打印当前拦截器数量
            int interceptorCount = sqlSessionFactory.getConfiguration().getInterceptors().size();
            log.info("📊 SqlSessionFactory [{}] 当前拦截器数量: {}", beanName, interceptorCount);
        }
        
        return bean;
    }
}
