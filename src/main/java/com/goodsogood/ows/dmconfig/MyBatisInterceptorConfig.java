package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis拦截器配置
 * 使用统一的SQL拦截器，整合了所有转换功能
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Configuration
@Log4j2
public class MyBatisInterceptorConfig {

    /**
     * 统一的SQL拦截器Bean
     */
    @Bean
    public UnifiedSqlInterceptor unifiedSqlInterceptor() {
        return new UnifiedSqlInterceptor();
    }

    /**
     * 使用ConfigurationCustomizer来注册统一的SQL拦截器
     * 这种方式可以确保在SqlSessionFactory创建时就注册拦截器
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return new ConfigurationCustomizer() {
            @Override
            public void customize(org.apache.ibatis.session.Configuration configuration) {
                log.info("=== ConfigurationCustomizer被调用，开始注册统一SQL拦截器 ===");

                // 注册统一的SQL拦截器
                log.info("注册UnifiedSqlInterceptor...");
                configuration.addInterceptor(unifiedSqlInterceptor());

                log.info("统一SQL拦截器注册完成，当前拦截器数量: {}", configuration.getInterceptors().size());

                // 打印所有拦截器信息
                for (int i = 0; i < configuration.getInterceptors().size(); i++) {
                    Interceptor interceptor = configuration.getInterceptors().get(i);
                    log.info("拦截器 #{}: {}", i + 1, interceptor.getClass().getName());
                }

                log.info("统一SQL拦截器整合了以下功能:");
                log.info("- MySQL语法转换 (原SqlCompatibilityUtil)");
                log.info("- 复杂SQL转换 (原ComplexSqlConverter)");
                log.info("- 变量初始化处理");
                log.info("- 关键字格式化");
                log.info("- 字段名保护");
            }
        };
    }

    /**
     * 备用方案：直接创建拦截器数组
     * 如果ConfigurationCustomizer不工作，可以尝试这种方式
     */
    @Bean
    public Interceptor[] interceptors() {
        log.info("=== 创建统一SQL拦截器数组 ===");
        return new Interceptor[]{
            unifiedSqlInterceptor()
        };
    }
}
