package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * SQL兼容性工具类
 * 用于将MySQL语法转换为达梦数据库兼容的语法
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Log4j2
public class SqlCompatibilityUtil {

    // 常用的MySQL到达梦SQL转换规则
    private static final Pattern LIMIT_PATTERN = Pattern.compile("\\s+LIMIT\\s+(\\d+)(?:\\s*,\\s*(\\d+))?", Pattern.CASE_INSENSITIVE);
    private static final Pattern BACKTICK_PATTERN = Pattern.compile("`([^`]+)`");
    private static final Pattern NOW_PATTERN = Pattern.compile("\\bNOW\\(\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern SYSDATE_PATTERN = Pattern.compile("\\bSYSDATE\\(\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern UNIX_TIMESTAMP_PATTERN = Pattern.compile("\\bUNIX_TIMESTAMP\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern UNIX_TIMESTAMP_NOW_PATTERN = Pattern.compile("\\bUNIX_TIMESTAMP\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern IFNULL_PATTERN = Pattern.compile("\\bIFNULL\\s*\\(([^,]+),([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CONCAT_PATTERN = Pattern.compile("\\bCONCAT\\s*\\(", Pattern.CASE_INSENSITIVE);
    private static final Pattern DATE_FORMAT_PATTERN = Pattern.compile("\\bDATE_FORMAT\\s*\\(([^,]+),\\s*'([^']+)'\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern AUTO_INCREMENT_PATTERN = Pattern.compile("\\bAUTO_INCREMENT\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern ENGINE_PATTERN = Pattern.compile("\\bENGINE\\s*=\\s*\\w+", Pattern.CASE_INSENSITIVE);
    private static final Pattern CHARSET_PATTERN = Pattern.compile("\\bCHARSET\\s*=\\s*\\w+", Pattern.CASE_INSENSITIVE);

    // MySQL变量语法转换
    private static final Pattern MYSQL_VARIABLE_PATTERN = Pattern.compile("@(\\w+)\\s*:?=\\s*@\\1\\s*\\+\\s*(\\d+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern MYSQL_VARIABLE_INIT_PATTERN = Pattern.compile("\\(SELECT\\s+@(\\w+)\\s*:?=\\s*(\\d+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern MYSQL_VARIABLE_USE_PATTERN = Pattern.compile("@(\\w+)", Pattern.CASE_INSENSITIVE);

    // ROW_NUMBER() 窗口函数相关
    private static final Pattern MYSQL_ROWINDEX_PATTERN = Pattern.compile("@rowindex\\s*:?=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", Pattern.CASE_INSENSITIVE);

    // 更多MySQL特有函数模式
    private static final Pattern GROUP_CONCAT_PATTERN = Pattern.compile("\\bGROUP_CONCAT\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern SUBSTRING_INDEX_PATTERN = Pattern.compile("\\bSUBSTRING_INDEX\\s*\\(([^,]+),([^,]+),([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern FIND_IN_SET_PATTERN = Pattern.compile("\\bFIND_IN_SET\\s*\\(([^,]+),([^)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CONCAT_WS_PATTERN = Pattern.compile("\\bCONCAT_WS\\s*\\(([^,]+),([^)]+)\\)", Pattern.CASE_INSENSITIVE);

    // JSON函数模式
    private static final Pattern JSON_EXTRACT_PATTERN = Pattern.compile("\\bJSON_EXTRACT\\s*\\(([^,]+),\\s*'([^']+)'\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern JSON_UNQUOTE_PATTERN = Pattern.compile("\\bJSON_UNQUOTE\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);

    // 正则表达式模式
    private static final Pattern REGEXP_PATTERN = Pattern.compile("\\b(\\w+)\\s+REGEXP\\s+'([^']+)'", Pattern.CASE_INSENSITIVE);
    private static final Pattern RLIKE_PATTERN = Pattern.compile("\\b(\\w+)\\s+RLIKE\\s+'([^']+)'", Pattern.CASE_INSENSITIVE);

    // 数学函数模式
    private static final Pattern POW_PATTERN = Pattern.compile("\\bPOW\\s*\\(", Pattern.CASE_INSENSITIVE);
    private static final Pattern LOG_PATTERN = Pattern.compile("\\bLOG\\s*\\(([^,)]+)\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern RAND_PATTERN = Pattern.compile("\\bRAND\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);

    // 特殊语法模式
    private static final Pattern ON_DUPLICATE_KEY_PATTERN = Pattern.compile("\\bON\\s+DUPLICATE\\s+KEY\\s+UPDATE\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern REPLACE_INTO_PATTERN = Pattern.compile("\\bREPLACE\\s+INTO\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern STRAIGHT_JOIN_PATTERN = Pattern.compile("\\bSTRAIGHT_JOIN\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern FORCE_INDEX_PATTERN = Pattern.compile("\\bFORCE\\s+INDEX\\s*\\([^)]+\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern USE_INDEX_PATTERN = Pattern.compile("\\bUSE\\s+INDEX\\s*\\([^)]+\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern IGNORE_INDEX_PATTERN = Pattern.compile("\\bIGNORE\\s+INDEX\\s*\\([^)]+\\)", Pattern.CASE_INSENSITIVE);

    // 特殊函数模式
    private static final Pattern SQL_CALC_FOUND_ROWS_PATTERN = Pattern.compile("\\bSQL_CALC_FOUND_ROWS\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern FOUND_ROWS_PATTERN = Pattern.compile("\\bFOUND_ROWS\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern LAST_INSERT_ID_PATTERN = Pattern.compile("\\bLAST_INSERT_ID\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CONNECTION_ID_PATTERN = Pattern.compile("\\bCONNECTION_ID\\s*\\(\\s*\\)", Pattern.CASE_INSENSITIVE);

    // 数据类型转换模式
    private static final Pattern CAST_SIGNED_PATTERN = Pattern.compile("\\bCAST\\s*\\(([^)]+)\\s+AS\\s+SIGNED\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CAST_UNSIGNED_PATTERN = Pattern.compile("\\bCAST\\s*\\(([^)]+)\\s+AS\\s+UNSIGNED\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CAST_CHAR_PATTERN = Pattern.compile("\\bCAST\\s*\\(([^)]+)\\s+AS\\s+CHAR\\)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CAST_DATETIME_PATTERN = Pattern.compile("\\bCAST\\s*\\(([^)]+)\\s+AS\\s+DATETIME\\)", Pattern.CASE_INSENSITIVE);

    // 更多数学函数
    private static final Pattern LOG10_PATTERN = Pattern.compile("\\bLOG10\\s*\\(", Pattern.CASE_INSENSITIVE);

    // JSON对象和数组函数
    private static final Pattern JSON_OBJECT_PATTERN = Pattern.compile("\\bJSON_OBJECT\\s*\\(", Pattern.CASE_INSENSITIVE);
    private static final Pattern JSON_ARRAY_PATTERN = Pattern.compile("\\bJSON_ARRAY\\s*\\(", Pattern.CASE_INSENSITIVE);

    // 正则表达式替换
    private static final Pattern REGEXP_REPLACE_PATTERN = Pattern.compile("\\bREGEXP_REPLACE\\s*\\(([^,]+),\\s*'([^']+)',\\s*'([^']+)'\\)", Pattern.CASE_INSENSITIVE);

    /**
     * 转换SQL语句，使其兼容达梦数据库
     * 
     * @param originalSql 原始MySQL SQL语句
     * @return 转换后的达梦兼容SQL语句
     */
    public static String convertSql(String originalSql) {
        if (originalSql == null || originalSql.trim().isEmpty()) {
            return originalSql;
        }

        String convertedSql = originalSql;
        
        try {
            // 1. 处理LIMIT语法 - MySQL: LIMIT offset, count -> 达梦: LIMIT count OFFSET offset
            convertedSql = convertLimit(convertedSql);
            
            // 2. 移除反引号 - MySQL使用反引号，达梦使用双引号或不使用
            convertedSql = convertBackticks(convertedSql);
            
            // 3. 转换时间函数
            convertedSql = convertTimeFunctions(convertedSql);
            
            // 4. 转换字符串函数
            convertedSql = convertStringFunctions(convertedSql);
            
            // 5. 转换其他函数
            convertedSql = convertOtherFunctions(convertedSql);
            
            // 6. 处理DDL语句
            convertedSql = convertDdlStatements(convertedSql);

            // 7. 处理MySQL变量语法
            convertedSql = convertMysqlVariables(convertedSql);

            // 8. 处理MySQL特有函数
            convertedSql = convertMysqlSpecificFunctions(convertedSql);

            // 9. 处理JSON函数
            convertedSql = convertJsonFunctions(convertedSql);

            // 10. 处理正则表达式
            convertedSql = convertRegexFunctions(convertedSql);

            // 11. 处理数学函数
            convertedSql = convertMathFunctions(convertedSql);

            // 12. 处理特殊语法
            convertedSql = convertSpecialSyntax(convertedSql);

            // 13. 处理特殊函数
            convertedSql = convertSpecialFunctions(convertedSql);

            // 14. 处理数据类型转换
            convertedSql = convertDataTypes(convertedSql);

            // 15. 最终SQL格式清理
            convertedSql = cleanupSqlFormat(convertedSql);

            // 记录转换日志（仅在SQL发生变化时）
            if (!originalSql.equals(convertedSql)) {
                log.debug("SQL转换: {} -> {}", originalSql.trim(), convertedSql.trim());
            }
            
        } catch (Exception e) {
            log.warn("SQL转换失败，使用原始SQL: {}", e.getMessage());
            return originalSql;
        }
        
        return convertedSql;
    }

    /**
     * 转换LIMIT语法
     * MySQL: LIMIT 10, 20 或 LIMIT 20 OFFSET 10
     * 达梦: LIMIT 20 OFFSET 10
     */
    private static String convertLimit(String sql) {
        return LIMIT_PATTERN.matcher(sql).replaceAll(matchResult -> {
            String group1 = matchResult.group(1); // 第一个数字
            String group2 = matchResult.group(2); // 第二个数字（可能为null）
            
            if (group2 != null) {
                // MySQL: LIMIT offset, count -> 达梦: LIMIT count OFFSET offset
                return " LIMIT " + group2 + " OFFSET " + group1;
            } else {
                // MySQL: LIMIT count -> 达梦: LIMIT count
                return " LIMIT " + group1;
            }
        });
    }

    /**
     * 转换反引号为双引号或移除
     */
    private static String convertBackticks(String sql) {
        // 移除反引号，达梦数据库通常不需要引号
        // 但如果表名或字段名是关键字，可能需要双引号
        return BACKTICK_PATTERN.matcher(sql).replaceAll("$1");
    }

    /**
     * 转换时间函数
     */
    private static String convertTimeFunctions(String sql) {
        // NOW() -> SYSDATE
        sql = NOW_PATTERN.matcher(sql).replaceAll("SYSDATE");
        
        // SYSDATE() -> SYSDATE (达梦中SYSDATE不需要括号)
        sql = SYSDATE_PATTERN.matcher(sql).replaceAll("SYSDATE");
        
        // UNIX_TIMESTAMP(date) -> EXTRACT(EPOCH FROM date)
        sql = UNIX_TIMESTAMP_PATTERN.matcher(sql).replaceAll("EXTRACT(EPOCH FROM $1)");
        
        // UNIX_TIMESTAMP() -> EXTRACT(EPOCH FROM SYSDATE)
        sql = UNIX_TIMESTAMP_NOW_PATTERN.matcher(sql).replaceAll("EXTRACT(EPOCH FROM SYSDATE)");
        
        // DATE_FORMAT(date, format) -> TO_CHAR(date, format)
        sql = DATE_FORMAT_PATTERN.matcher(sql).replaceAll("TO_CHAR($1, '$2')");
        
        return sql;
    }

    /**
     * 转换字符串函数
     */
    private static String convertStringFunctions(String sql) {
        // IFNULL(expr1, expr2) -> NVL(expr1, expr2)
        sql = IFNULL_PATTERN.matcher(sql).replaceAll("NVL($1,$2)");
        
        // CONCAT函数在达梦中也支持，但可以使用||操作符
        // 这里保持CONCAT不变，因为达梦支持
        
        return sql;
    }

    /**
     * 转换其他函数
     */
    private static String convertOtherFunctions(String sql) {
        // 这里可以添加其他函数的转换规则
        return sql;
    }

    /**
     * 转换DDL语句
     */
    private static String convertDdlStatements(String sql) {
        // 移除AUTO_INCREMENT关键字，达梦使用IDENTITY
        sql = AUTO_INCREMENT_PATTERN.matcher(sql).replaceAll("IDENTITY(1,1)");

        // 移除ENGINE子句
        sql = ENGINE_PATTERN.matcher(sql).replaceAll("");

        // 移除CHARSET子句
        sql = CHARSET_PATTERN.matcher(sql).replaceAll("");

        return sql;
    }

    /**
     * 转换MySQL变量语法
     * 将MySQL的用户变量转换为达梦兼容的语法
     */
    private static String convertMysqlVariables(String sql) {
        // 处理 @rowindex := @rowindex + 1 AS rowindex 这种模式
        // 转换为 ROW_NUMBER() OVER(ORDER BY ...) AS rowindex
        // 确保语法正确：ROW_NUMBER() OVER(ORDER BY 1)
        sql = MYSQL_ROWINDEX_PATTERN.matcher(sql).replaceAll("ROW_NUMBER() OVER(ORDER BY 1) AS $1");

        // 处理更复杂的情况，如果包含 @rowindex 变量，尝试用ROW_NUMBER()替换
        if (sql.toLowerCase().contains("@rowindex")) {
            log.info("检测到@rowindex变量，开始转换...");

            // 先移除变量初始化部分 - 处理各种初始化模式
            // 更全面的初始化移除，包括各种可能的格式

            // 首先处理最具体的问题模式，避免被通用模式误处理
            // 处理各种空格变体，包括多个空格的情况
            sql = sql.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
            sql = sql.replaceAll("(?i),\\s*\\(Select @rowindex:=-1\\) b\\s*", " ");
            sql = sql.replace(",(Select @rowindex:=-1) b", "");
            sql = sql.replace(", (Select @rowindex:=-1) b", "");
            sql = sql.replace(",(SELECT @rowindex:=-1) b", "");
            sql = sql.replace(", (SELECT @rowindex:=-1) b", "");
            sql = sql.replace(",(select @rowindex:=-1) b", "");
            sql = sql.replace(", (select @rowindex:=-1) b", "");

            String[] initPatterns = {
                // 带逗号的模式：, (SELECT @rowindex := 0) r 或 ,(SELECT @rowindex := 0) r
                "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*0\\s*\\)\\s*\\w*",
                "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*",
                "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*\\d+\\s*\\)\\s*\\w*",
                "(?i),\\s*\\(\\s*SELECT\\s+@\\w+\\s*:?\\s*=\\s*-?\\d+\\s*\\)\\s*\\w*",

                // 紧密连接的逗号模式：grades,(Select @rowindex:=-1) b
                "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
                "(?i),\\(\\s*SELECT\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
                "(?i),\\(\\s*select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",

                // 不带逗号的模式：(SELECT @rowindex := 0) r
                "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*0\\s*\\)\\s*\\w*",
                "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*",
                "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*\\d+\\s*\\)\\s*\\w*",
                "(?i)\\(\\s*SELECT\\s+@\\w+\\s*:?\\s*=\\s*-?\\d+\\s*\\)\\s*\\w*",

                // 特殊情况：(Select @rowindex:=-1) b - 注意Select的大小写和紧密的:=
                "(?i)\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
                "(?i)\\(\\s*select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",

                // 多变量初始化
                "(?i),\\s*\\(\\s*SELECT\\s+@\\w+\\s*:?\\s*=\\s*-?\\d+(?:\\s*,\\s*@\\w+\\s*:?\\s*=\\s*-?\\d+)*\\s*\\)\\s*\\w*",
                "(?i)\\(\\s*SELECT\\s+@\\w+\\s*:?\\s*=\\s*-?\\d+(?:\\s*,\\s*@\\w+\\s*:?\\s*=\\s*-?\\d+)*\\s*\\)\\s*\\w*"
            };

            for (String pattern : initPatterns) {
                String before = sql;
                sql = sql.replaceAll(pattern, "");
                if (!before.equals(sql)) {
                    log.debug("移除初始化语句，使用模式: {}", pattern);
                }
            }

            // 如果SQL中包含 @rowindex := @rowindex + 1，替换为 ROW_NUMBER() OVER()
            // 处理各种空格组合：@rowindex := 、@rowindex : = 、@rowindex:= 等
            if (sql.toLowerCase().contains("order by")) {
                // 尝试提取ORDER BY子句
                String orderByClause = extractOrderByClause(sql);
                if (orderByClause != null && !orderByClause.trim().isEmpty()) {
                    sql = sql.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
                                       "ROW_NUMBER() OVER(" + orderByClause + ") AS $1");
                } else {
                    sql = sql.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
                                       "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
                }
            } else {
                sql = sql.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)",
                                   "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
            }

            // 如果还有@rowindex残留，进行更精确的清理
            if (sql.toLowerCase().contains("@rowindex")) {
                log.info("进行@rowindex残留清理...");
                // 只匹配明确的@rowindex := @rowindex + 1模式，避免误匹配初始化语句
                // 必须包含两个@rowindex和+号，确保是递增模式
                sql = sql.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", "ROW_NUMBER() OVER(ORDER BY 1) AS $1");

                // 更精确的残留清理，必须匹配完整的递增模式
                sql = sql.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1(?=\\s|,|$|\\))", "ROW_NUMBER() OVER(ORDER BY 1)");

                // 如果仍有残留，只清理明确不是初始化的@rowindex
                if (sql.toLowerCase().contains("@rowindex")) {
                    // 只替换不在初始化语句中的@rowindex（不在SELECT...=数字的模式中）
                    // 改进负向前瞻，包含负数的情况
                    sql = sql.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
                    log.warn("发现无法精确转换的@rowindex，已替换为常量1");
                }
            }

            // 清理多余的逗号和空格
            sql = sql.replaceAll(",\\s*FROM", " FROM");
            sql = sql.replaceAll(",\\s*,", ",");

            // 确保关键字之间有适当的空格 - 使用更安全的方法
            // 处理SELECT和ROW_NUMBER连接的问题
            sql = sql.replaceAll("(?i)SELECT\\s*ROW_NUMBER", "SELECT ROW_NUMBER");
            sql = sql.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");

            // 处理ROW_NUMBER和OVER连接的问题
            sql = sql.replaceAll("(?i)ROW_NUMBER\\s*OVER", "ROW_NUMBER() OVER");
            sql = sql.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");

            // 确保ROW_NUMBER后面有括号
            sql = sql.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");

            // 处理OVER和括号的问题
            sql = sql.replaceAll("(?i)OVER\\s*\\(", "OVER(");
            sql = sql.replaceAll("(?i)OVER(?!\\s*\\()", "OVER(ORDER BY 1)");

            // 最后清理多余的空格
            sql = sql.replaceAll("\\s+", " ").trim();

            log.info("@rowindex变量转换完成");
        }

        // 处理其他MySQL变量，但要避免误匹配已转换的内容
        // 只移除明确的MySQL变量赋值，排除@rowindex（已处理）和系统变量（@@开头）
        if (!sql.contains("ROW_NUMBER")) {
            // 只在没有ROW_NUMBER的情况下清理其他变量，避免误匹配
            sql = sql.replaceAll("(?i)@(?!rowindex)\\w+\\s*:?=\\s*[^,\\s]+", "");
        }

        return sql;
    }

    /**
     * 提取ORDER BY子句
     */
    private static String extractOrderByClause(String sql) {
        try {
            // 简单的ORDER BY提取逻辑
            String lowerSql = sql.toLowerCase();
            int orderByIndex = lowerSql.lastIndexOf("order by");
            if (orderByIndex != -1) {
                String orderByPart = sql.substring(orderByIndex);
                // 提取到下一个关键字或结束
                String[] keywords = {"limit", "offset", ")", ";"};
                for (String keyword : keywords) {
                    int keywordIndex = orderByPart.toLowerCase().indexOf(keyword);
                    if (keywordIndex != -1) {
                        orderByPart = orderByPart.substring(0, keywordIndex).trim();
                        break;
                    }
                }
                return orderByPart.trim();
            }
        } catch (Exception e) {
            log.warn("提取ORDER BY子句失败: {}", e.getMessage());
        }
        return "ORDER BY 1";
    }

    /**
     * 转换MySQL特有函数
     */
    private static String convertMysqlSpecificFunctions(String sql) {
        // 1. GROUP_CONCAT -> LISTAGG
        sql = GROUP_CONCAT_PATTERN.matcher(sql).replaceAll("LISTAGG($1, ',') WITHIN GROUP (ORDER BY $1)");

        // 2. SUBSTRING_INDEX -> 自定义实现（简化处理）
        sql = SUBSTRING_INDEX_PATTERN.matcher(sql).replaceAll("SUBSTR($1, 1, INSTR($1, $2) - 1)");

        // 3. FIND_IN_SET -> INSTR实现
        sql = FIND_IN_SET_PATTERN.matcher(sql).replaceAll("CASE WHEN INSTR(',' || $2 || ',', ',' || $1 || ',') > 0 THEN 1 ELSE 0 END");

        // 4. CONCAT_WS -> 自定义实现
        sql = CONCAT_WS_PATTERN.matcher(sql).replaceAll("CONCAT($1, $2)");

        return sql;
    }

    /**
     * 转换JSON函数
     */
    private static String convertJsonFunctions(String sql) {
        // 1. JSON_EXTRACT -> JSON_VALUE
        sql = JSON_EXTRACT_PATTERN.matcher(sql).replaceAll("JSON_VALUE($1, '$2')");

        // 2. JSON_UNQUOTE -> JSON_VALUE
        sql = JSON_UNQUOTE_PATTERN.matcher(sql).replaceAll("JSON_VALUE($1, '$')");

        // 3. JSON_OBJECT -> 保持不变（达梦支持）
        // 如果达梦不支持，可以启用下面的转换
        // sql = JSON_OBJECT_PATTERN.matcher(sql).replaceAll("JSON_OBJECT(");

        // 4. JSON_ARRAY -> 保持不变（达梦支持）
        // 如果达梦不支持，可以启用下面的转换
        // sql = JSON_ARRAY_PATTERN.matcher(sql).replaceAll("JSON_ARRAY(");

        return sql;
    }

    /**
     * 转换正则表达式函数
     */
    private static String convertRegexFunctions(String sql) {
        // 1. REGEXP -> REGEXP_LIKE
        sql = REGEXP_PATTERN.matcher(sql).replaceAll("REGEXP_LIKE($1, '$2')");

        // 2. RLIKE -> REGEXP_LIKE
        sql = RLIKE_PATTERN.matcher(sql).replaceAll("REGEXP_LIKE($1, '$2')");

        // 3. REGEXP_REPLACE -> REGEXP_REPLACE (达梦支持)
        sql = REGEXP_REPLACE_PATTERN.matcher(sql).replaceAll("REGEXP_REPLACE($1, '$2', '$3')");

        return sql;
    }

    /**
     * 转换数学函数
     */
    private static String convertMathFunctions(String sql) {
        // 1. POW -> POWER
        sql = POW_PATTERN.matcher(sql).replaceAll("POWER(");

        // 2. LOG -> LN (自然对数)
        sql = LOG_PATTERN.matcher(sql).replaceAll("LN($1)");

        // 3. LOG10 -> LOG (以10为底)
        sql = LOG10_PATTERN.matcher(sql).replaceAll("LOG(10, ");

        // 4. RAND -> RANDOM
        sql = RAND_PATTERN.matcher(sql).replaceAll("RANDOM()");

        return sql;
    }

    /**
     * 转换特殊语法
     */
    private static String convertSpecialSyntax(String sql) {
        // 1. ON DUPLICATE KEY UPDATE -> ON CONFLICT DO UPDATE SET
        sql = ON_DUPLICATE_KEY_PATTERN.matcher(sql).replaceAll("ON CONFLICT DO UPDATE SET");

        // 2. REPLACE INTO -> INSERT OR REPLACE INTO
        sql = REPLACE_INTO_PATTERN.matcher(sql).replaceAll("INSERT OR REPLACE INTO");

        // 3. STRAIGHT_JOIN -> JOIN
        sql = STRAIGHT_JOIN_PATTERN.matcher(sql).replaceAll("JOIN");

        // 4. 移除索引提示
        sql = FORCE_INDEX_PATTERN.matcher(sql).replaceAll("");
        sql = USE_INDEX_PATTERN.matcher(sql).replaceAll("");
        sql = IGNORE_INDEX_PATTERN.matcher(sql).replaceAll("");

        return sql;
    }

    /**
     * 转换特殊函数
     */
    private static String convertSpecialFunctions(String sql) {
        // 1. 移除SQL_CALC_FOUND_ROWS
        sql = SQL_CALC_FOUND_ROWS_PATTERN.matcher(sql).replaceAll("");

        // 2. FOUND_ROWS() -> @@ROWCOUNT
        sql = FOUND_ROWS_PATTERN.matcher(sql).replaceAll("@@ROWCOUNT");

        // 3. LAST_INSERT_ID() -> SCOPE_IDENTITY()
        sql = LAST_INSERT_ID_PATTERN.matcher(sql).replaceAll("SCOPE_IDENTITY()");

        // 4. CONNECTION_ID() -> @@SPID
        sql = CONNECTION_ID_PATTERN.matcher(sql).replaceAll("@@SPID");

        return sql;
    }

    /**
     * 转换数据类型
     */
    private static String convertDataTypes(String sql) {
        // 1. CAST(x AS SIGNED) -> CAST(x AS INTEGER)
        sql = CAST_SIGNED_PATTERN.matcher(sql).replaceAll("CAST($1 AS INTEGER)");

        // 2. CAST(x AS UNSIGNED) -> CAST(x AS INTEGER)
        sql = CAST_UNSIGNED_PATTERN.matcher(sql).replaceAll("CAST($1 AS INTEGER)");

        // 3. CAST(x AS CHAR) -> CAST(x AS VARCHAR)
        sql = CAST_CHAR_PATTERN.matcher(sql).replaceAll("CAST($1 AS VARCHAR)");

        // 4. CAST(x AS DATETIME) -> CAST(x AS TIMESTAMP)
        sql = CAST_DATETIME_PATTERN.matcher(sql).replaceAll("CAST($1 AS TIMESTAMP)");

        return sql;
    }

    /**
     * 清理SQL格式问题
     */
    private static String cleanupSqlFormat(String sql) {
        // 1. 处理换行符和制表符
        sql = sql.replaceAll("\\r\\n|\\r|\\n", " ");
        sql = sql.replaceAll("\\t", " ");

        // 2. 处理IN子句中的换行和多余空格
        // 先处理IN子句内部的格式
        Pattern inPattern = Pattern.compile("(?i)\\bIN\\s*\\(([^)]+)\\)");
        Matcher inMatcher = inPattern.matcher(sql);
        StringBuffer sb = new StringBuffer();
        while (inMatcher.find()) {
            String inClause = inMatcher.group(1);
            // 清理IN子句内部的换行和多余空格
            inClause = inClause.replaceAll("\\s+", " ").trim();
            // 确保逗号后有空格
            inClause = inClause.replaceAll(",\\s*", ", ");
            inMatcher.appendReplacement(sb, "IN (" + inClause + ")");
        }
        inMatcher.appendTail(sb);
        sql = sb.toString();

        // 3. 确保关键字之间有适当的空格 - 使用更安全的方法
        // 先处理可能的关键字连接问题，确保在关键字前后有适当的空格

        // 处理SELECT和ROW_NUMBER连接的问题
        sql = sql.replaceAll("(?i)SELECT\\s*ROW_NUMBER", "SELECT ROW_NUMBER");
        sql = sql.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");

        // 处理ROW_NUMBER和OVER连接的问题
        sql = sql.replaceAll("(?i)ROW_NUMBER\\s*OVER", "ROW_NUMBER() OVER");
        sql = sql.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");

        // 确保ROW_NUMBER后面有括号
        sql = sql.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");

        // 处理OVER和括号的问题
        sql = sql.replaceAll("(?i)OVER\\s*\\(", "OVER(");
        sql = sql.replaceAll("(?i)OVER(?!\\s*\\()", "OVER(ORDER BY 1)");

        // 4. 清理多余的空格
        sql = sql.replaceAll("\\s+", " ");

        // 5. 清理括号周围的空格
        sql = sql.replaceAll("\\s*\\(\\s*", " (");
        sql = sql.replaceAll("\\s*\\)\\s*", ") ");

        // 6. 清理逗号周围的空格
        sql = sql.replaceAll("\\s*,\\s*", ", ");

        return sql.trim();
    }

    /**
     * 检查SQL是否需要转换
     */
    public static boolean needsConversion(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String upperSql = sql.toUpperCase();

        // 检查是否包含需要转换的MySQL特有语法
        return upperSql.contains("LIMIT") ||
               sql.contains("`") ||
               upperSql.contains("NOW()") ||
               upperSql.contains("UNIX_TIMESTAMP") ||
               upperSql.contains("IFNULL") ||
               upperSql.contains("DATE_FORMAT") ||
               upperSql.contains("AUTO_INCREMENT") ||
               upperSql.contains("ENGINE=") ||
               upperSql.contains("CHARSET=") ||
               sql.contains("@") ||  // MySQL变量语法
               upperSql.contains("@ROWINDEX") ||
               // MySQL特有函数
               upperSql.contains("GROUP_CONCAT") ||
               upperSql.contains("SUBSTRING_INDEX") ||
               upperSql.contains("FIND_IN_SET") ||
               upperSql.contains("CONCAT_WS") ||
               // JSON函数
               upperSql.contains("JSON_EXTRACT") ||
               upperSql.contains("JSON_UNQUOTE") ||
               upperSql.contains("JSON_OBJECT") ||
               upperSql.contains("JSON_ARRAY") ||
               // 正则表达式
               upperSql.contains(" REGEXP ") ||
               upperSql.contains(" RLIKE ") ||
               upperSql.contains("REGEXP_REPLACE") ||
               // 数学函数
               upperSql.contains("POW(") ||
               upperSql.contains("LOG(") ||
               upperSql.contains("LOG10(") ||
               upperSql.contains("RAND()") ||
               // 特殊语法
               upperSql.contains("ON DUPLICATE KEY") ||
               upperSql.contains("REPLACE INTO") ||
               upperSql.contains("STRAIGHT_JOIN") ||
               upperSql.contains("FORCE INDEX") ||
               upperSql.contains("USE INDEX") ||
               upperSql.contains("IGNORE INDEX") ||
               // 特殊函数
               upperSql.contains("SQL_CALC_FOUND_ROWS") ||
               upperSql.contains("FOUND_ROWS()") ||
               upperSql.contains("LAST_INSERT_ID()") ||
               upperSql.contains("CONNECTION_ID()") ||
               // 数据类型转换
               (upperSql.contains("CAST(") && (upperSql.contains("SIGNED") || upperSql.contains("UNSIGNED")));
    }

    /**
     * 获取SQL类型
     */
    public static SqlType getSqlType(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return SqlType.UNKNOWN;
        }
        
        String trimmedSql = sql.trim().toUpperCase();
        
        if (trimmedSql.startsWith("SELECT")) {
            return SqlType.SELECT;
        } else if (trimmedSql.startsWith("INSERT")) {
            return SqlType.INSERT;
        } else if (trimmedSql.startsWith("UPDATE")) {
            return SqlType.UPDATE;
        } else if (trimmedSql.startsWith("DELETE")) {
            return SqlType.DELETE;
        } else if (trimmedSql.startsWith("CREATE")) {
            return SqlType.CREATE;
        } else if (trimmedSql.startsWith("ALTER")) {
            return SqlType.ALTER;
        } else if (trimmedSql.startsWith("DROP")) {
            return SqlType.DROP;
        } else {
            return SqlType.OTHER;
        }
    }

    /**
     * SQL类型枚举
     */
    public enum SqlType {
        SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP, OTHER, UNKNOWN
    }
}
