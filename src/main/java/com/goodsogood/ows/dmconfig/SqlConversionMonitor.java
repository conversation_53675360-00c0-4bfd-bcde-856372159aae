package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;

/**
 * SQL转换监控器
 * 监控和统计SQL转换的性能和效果
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Component
@Log4j2
public class SqlConversionMonitor {

    // 统计计数器
    private final AtomicLong totalConversions = new AtomicLong(0);
    private final AtomicLong successfulConversions = new AtomicLong(0);
    private final AtomicLong failedConversions = new AtomicLong(0);
    private final AtomicLong totalConversionTime = new AtomicLong(0);
    
    // 转换类型统计
    private final Map<String, AtomicLong> conversionTypeStats = new ConcurrentHashMap<>();
    
    // 错误统计
    private final Map<String, AtomicLong> errorStats = new ConcurrentHashMap<>();
    
    // 性能统计
    private final Map<String, Long> performanceStats = new ConcurrentHashMap<>();

    /**
     * 记录SQL转换开始
     */
    public long recordConversionStart(String sqlType) {
        totalConversions.incrementAndGet();
        conversionTypeStats.computeIfAbsent(sqlType, k -> new AtomicLong(0)).incrementAndGet();
        return System.currentTimeMillis();
    }

    /**
     * 记录SQL转换成功
     */
    public void recordConversionSuccess(String sqlType, long startTime, String originalSql, String convertedSql) {
        long duration = System.currentTimeMillis() - startTime;
        totalConversionTime.addAndGet(duration);
        successfulConversions.incrementAndGet();
        
        // 记录性能统计
        String perfKey = sqlType + "_avg_time";
        performanceStats.merge(perfKey, duration, (oldVal, newVal) -> (oldVal + newVal) / 2);
        
        // 记录详细日志（仅在DEBUG级别）
        if (log.isDebugEnabled()) {
            log.debug("SQL转换成功 [{}] 耗时: {}ms", sqlType, duration);
            log.debug("原始SQL: {}", originalSql.length() > 100 ? originalSql.substring(0, 100) + "..." : originalSql);
            log.debug("转换SQL: {}", convertedSql.length() > 100 ? convertedSql.substring(0, 100) + "..." : convertedSql);
        }
    }

    /**
     * 记录SQL转换失败
     */
    public void recordConversionFailure(String sqlType, long startTime, String error, String originalSql) {
        long duration = System.currentTimeMillis() - startTime;
        totalConversionTime.addAndGet(duration);
        failedConversions.incrementAndGet();
        
        // 记录错误统计
        errorStats.computeIfAbsent(error, k -> new AtomicLong(0)).incrementAndGet();
        
        log.warn("SQL转换失败 [{}] 耗时: {}ms, 错误: {}", sqlType, duration, error);
        log.warn("失败SQL: {}", originalSql.length() > 200 ? originalSql.substring(0, 200) + "..." : originalSql);
    }

    /**
     * 获取转换统计信息
     */
    public ConversionStats getStats() {
        ConversionStats stats = new ConversionStats();
        stats.totalConversions = totalConversions.get();
        stats.successfulConversions = successfulConversions.get();
        stats.failedConversions = failedConversions.get();
        stats.successRate = stats.totalConversions > 0 ? 
            (double) stats.successfulConversions / stats.totalConversions * 100 : 0;
        stats.averageConversionTime = stats.totalConversions > 0 ? 
            (double) totalConversionTime.get() / stats.totalConversions : 0;
        
        // 复制统计数据
        stats.conversionTypeStats = new ConcurrentHashMap<>();
        conversionTypeStats.forEach((k, v) -> stats.conversionTypeStats.put(k, v.get()));
        
        stats.errorStats = new ConcurrentHashMap<>();
        errorStats.forEach((k, v) -> stats.errorStats.put(k, v.get()));
        
        stats.performanceStats = new ConcurrentHashMap<>(performanceStats);
        
        return stats;
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalConversions.set(0);
        successfulConversions.set(0);
        failedConversions.set(0);
        totalConversionTime.set(0);
        conversionTypeStats.clear();
        errorStats.clear();
        performanceStats.clear();
        
        log.info("SQL转换统计信息已重置");
    }

    /**
     * 打印统计报告
     */
    public void printStatsReport() {
        ConversionStats stats = getStats();
        
        log.info("=== SQL转换统计报告 ===");
        log.info("总转换次数: {}", stats.totalConversions);
        log.info("成功转换: {}", stats.successfulConversions);
        log.info("失败转换: {}", stats.failedConversions);
        log.info("成功率: {:.2f}%", stats.successRate);
        log.info("平均转换时间: {:.2f}ms", stats.averageConversionTime);
        
        if (!stats.conversionTypeStats.isEmpty()) {
            log.info("--- 转换类型统计 ---");
            stats.conversionTypeStats.forEach((type, count) -> 
                log.info("{}: {} 次", type, count));
        }
        
        if (!stats.errorStats.isEmpty()) {
            log.info("--- 错误统计 ---");
            stats.errorStats.forEach((error, count) -> 
                log.info("{}: {} 次", error, count));
        }
        
        if (!stats.performanceStats.isEmpty()) {
            log.info("--- 性能统计 ---");
            stats.performanceStats.forEach((metric, value) -> 
                log.info("{}: {}ms", metric, value));
        }
        
        log.info("========================");
    }

    /**
     * 转换统计数据类
     */
    public static class ConversionStats {
        public long totalConversions;
        public long successfulConversions;
        public long failedConversions;
        public double successRate;
        public double averageConversionTime;
        public Map<String, Long> conversionTypeStats;
        public Map<String, Long> errorStats;
        public Map<String, Long> performanceStats;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("ConversionStats{");
            sb.append("total=").append(totalConversions);
            sb.append(", success=").append(successfulConversions);
            sb.append(", failed=").append(failedConversions);
            sb.append(", successRate=").append(String.format("%.2f%%", successRate));
            sb.append(", avgTime=").append(String.format("%.2fms", averageConversionTime));
            sb.append("}");
            return sb.toString();
        }
    }

    /**
     * 检查是否需要性能警告
     */
    public void checkPerformanceWarnings() {
        ConversionStats stats = getStats();
        
        // 成功率过低警告
        if (stats.totalConversions > 100 && stats.successRate < 90) {
            log.warn("⚠️ SQL转换成功率过低: {:.2f}%，建议检查转换规则", stats.successRate);
        }
        
        // 平均转换时间过长警告
        if (stats.averageConversionTime > 100) {
            log.warn("⚠️ SQL转换平均耗时过长: {:.2f}ms，建议优化转换逻辑", stats.averageConversionTime);
        }
        
        // 错误频率过高警告
        if (stats.failedConversions > stats.successfulConversions * 0.1) {
            log.warn("⚠️ SQL转换失败次数过多: {}次，建议检查错误原因", stats.failedConversions);
        }
    }
}
