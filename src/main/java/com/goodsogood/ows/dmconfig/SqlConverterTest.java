package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;

/**
 * SQL转换器测试类
 * 用于测试复杂SQL的转换效果
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Log4j2
public class SqlConverterTest {

    public static void main(String[] args) {
        testComplexSql();
//        testSimpleRowIndex();
//        testMysqlFunctions();
//        testMysqlVariables();
    }

    public static void testComplexSql() {
        // 测试您提到的复杂SQL
        String complexSql = "select L.orgId,score1 partyIndex, score2 businessIndex, score3 InnovationIndex from (select tmp1.org_id orgId, round(NVL(tmp1.score1/tmp2.scoreMedian1*100,0),4) score1, round(NVL(tmp1.score2/tmp2.scoreMedian2*100,0),4) score2, round(NVL(tmp1.score3/tmp2.scoreMedian3*100,0),4) score3  from (select t1.organization_id org_id,NVL(t2.score1,0) score1,NVL(t2.score2,0) score2,NVL(t2.score3,0) score3 from ( select organization_id  from t_organization  where  organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1 ) t1 LEFT JOIN ( select tt1.score_org_id, sum(case when tt1.parent_score_type=1 then tt1.total else 0 end) score1,  sum(case when tt1.parent_score_type=2 then tt1.total else 0 end) score2,  sum(case when tt1.parent_score_type=3 then tt1.total else 0 end) score3  from t_score_org_type_count tt1 INNER JOIN t_organization tt2 on tt1.score_org_id = tt2.organization_id  and tt1.org_id =3 and tt1.score_org_type =1 and tt1.parent_score_type in (1,2,3) and tt2.org_type_child in (10280304,10280309,10280314,10280315,10280319) and tt2.organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and tt2.status=1 GROUP BY tt1.score_org_id ) t2 on t1.organization_id = t2.score_org_id ) tmp1,( select scoreMedian1,scoreMedian2,scoreMedian3 from (  SELECT AVG(g.score1) scoreMedian1 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score1 AS score1   FROM (select score1 from (  select sum(NVL(tt1.total,0)) score1 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and    org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  ) tmmp where score1 !=0  union all  select score1 from (  select DISTINCT sum(NVL(tt1.total,0)) score1 from (select organization_id from t_organiza\n" +
                "tion where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =1 GROUP BY tt3.organization_id  ) tmmp2 where score1 =0  ORDER BY score1) grades,(Select @rowindex:=-1) b  ORDER BY grades.score1) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  ) t1,(  SELECT AVG(g.score2) scoreMedian2 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score2 AS score2   FROM (select score2 from (  select sum(NVL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id  ) tmmp where score2 !=0  union all  select score2 from (  select DISTINCT sum(NVL(tt1.total,0)) score2 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =2 GROUP BY tt3.organization_id  ) tmmp2 where score2 =0  ORDER BY score2) grades,(Select @rowindex:=-1) b  ORDER BY grades.score2) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  ) t2,(  SELECT AVG(g.score3) scoreMedian3 FROM (SELECT@rowindex:=@rowindex + 1 AS rowindex, grades.score3 AS score3   FROM (select score3 from (  select sum(NVL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and    org_type_child in (10280\n" +
                "304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id  ) tmmp where score3 !=0  union all  select score3 from (  select DISTINCT sum(NVL(tt1.total,0)) score3 from (select organization_id from t_organization where organization_id not in(7,8,2063,2091,2418,2575,2576,2650) and   org_type_child in (10280304,10280309,10280314,10280315,10280319) and status=1) tt3 LEFT JOIN t_score_org_type_count tt1 on tt1.score_org_id=tt3.organization_id and tt1.org_id=3 and tt1.score_org_type =1 and tt1.parent_score_type =3 GROUP BY tt3.organization_id  ) tmmp2 where score3 =0  ORDER BY score3) grades,(Select @rowindex:=-1) b  ORDER BY grades.score3) AS g WHERE g.rowindex IN(FLOOR(@rowindex / 2) , CEIL(@rowindex / 2))  ) t3) tmp2 ORDER BY org_id  )as L WHERE 1=1   and orgId IN  ( 4546,4549  )";

        log.info("=== SQL转换测试 ===");
        log.info("原始SQL: {}", complexSql);
        
        // 测试基础转换器
        String basicConverted = SqlCompatibilityUtil.convertSql(complexSql);
        log.info("基础转换结果: {}", basicConverted);
        
        // 测试复杂转换器
        String complexConverted = ComplexSqlConverter.convertComplexSql(complexSql);
        log.info("复杂转换结果: {}", complexConverted);
        
        // 测试智能转换器
        String smartConverted = ComplexSqlConverter.smartConvert(complexSql);
        log.info("智能转换结果: {}", smartConverted);
        
        // 检查是否需要转换
        boolean needsConversion = SqlCompatibilityUtil.needsConversion(complexSql);
        boolean isComplex = ComplexSqlConverter.isComplexSql(complexSql);
        
        log.info("需要转换: {}", needsConversion);
        log.info("是复杂SQL: {}", isComplex);
    }

    /**
     * 测试简单的@rowindex模式
     */
    public static void testSimpleRowIndex() {
        String sql = "SELECT @rowindex := @rowindex + 1 AS rowindex, name FROM users, (SELECT @rowindex := 0) r";
        
        log.info("=== 简单@rowindex测试 ===");
        log.info("原始SQL: {}", sql);
        
        String converted = ComplexSqlConverter.convertComplexSql(sql);
        log.info("转换结果: {}", converted);
    }

    /**
     * 测试MySQL函数转换
     */
    public static void testMysqlFunctions() {
        String[] testSqls = {
            "SELECT IFNULL(name, 'Unknown') FROM users",
            "SELECT GROUP_CONCAT(name) FROM categories",
            "SELECT JSON_EXTRACT(data, '$.name') FROM json_table",
            "SELECT JSON_UNQUOTE(JSON_EXTRACT(data, '$.email')) FROM json_table",
            "SELECT JSON_OBJECT('name', name, 'age', age) FROM users",
            "SELECT JSON_ARRAY(id, name, email) FROM users",
            "SELECT * FROM users WHERE email REGEXP '^[a-zA-Z0-9]+@'",
            "SELECT REGEXP_REPLACE(phone, '[^0-9]', '') FROM contacts",
            "SELECT POW(2, 3) AS result",
            "SELECT LOG10(100) AS result",
            "SELECT RAND() AS random_value",
            "SELECT CAST(price AS SIGNED) FROM products",
            "INSERT INTO users (name) VALUES ('John') ON DUPLICATE KEY UPDATE name = VALUES(name)",
            "SELECT SQL_CALC_FOUND_ROWS * FROM users LIMIT 10",
            "SELECT FOUND_ROWS() AS total"
        };

        log.info("=== MySQL函数转换测试 ===");

        for (int i = 0; i < testSqls.length; i++) {
            String sql = testSqls[i];
            log.info("测试 {}: {}", i + 1, sql);

            String converted = SqlCompatibilityUtil.convertSql(sql);
            log.info("转换结果: {}", converted);
            log.info("需要转换: {}", SqlCompatibilityUtil.needsConversion(sql));
            log.info("---");
        }
    }

    /**
     * 测试MySQL变量语法转换
     */
    public static void testMysqlVariables() {
        String[] testSqls = {
            "SELECT @rowindex := @rowindex + 1 AS rowindex FROM table",
            "SELECT @rowindex : = @rowindex + 1 AS rowindex FROM table",
            "SELECT @rowindex:=@rowindex+1 AS rowindex FROM table",
            "SELECT @var := 'value' FROM table",
            "SELECT @rowindex := @rowindex + 1 AS rn, data FROM table, (SELECT @rowindex := 0) r ORDER BY data"
        };

        log.info("=== MySQL变量语法转换测试 ===");

        for (int i = 0; i < testSqls.length; i++) {
            String sql = testSqls[i];
            log.info("测试 {}: {}", i + 1, sql);

            String converted = SqlCompatibilityUtil.convertSql(sql);
            log.info("转换结果: {}", converted);
            log.info("包含@符号: {}", converted.contains("@"));
            log.info("---");
        }
    }
}
