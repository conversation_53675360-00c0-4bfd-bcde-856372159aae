package com.goodsogood.ows.dmconfig;

import lombok.extern.log4j.Log4j2;

import java.util.*;
import java.util.regex.Pattern;

/**
 * SQL模式检测器
 * 检测SQL中的各种MySQL特有模式和复杂情况
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Log4j2
public class SqlPatternDetector {

    // 各种MySQL模式
    private static final Map<String, Pattern> MYSQL_PATTERNS = new HashMap<>();
    
    static {
        // 变量模式
        MYSQL_PATTERNS.put("USER_VARIABLE", Pattern.compile("@\\w+", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("VARIABLE_ASSIGNMENT", Pattern.compile("@\\w+\\s*:?\\s*=", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("ROWINDEX_PATTERN", Pattern.compile("@rowindex", Pattern.CASE_INSENSITIVE));
        
        // 函数模式
        MYSQL_PATTERNS.put("IFNULL", Pattern.compile("\\bIFNULL\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("UNIX_TIMESTAMP", Pattern.compile("\\bUNIX_TIMESTAMP\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("DATE_FORMAT", Pattern.compile("\\bDATE_FORMAT\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("CONCAT_WS", Pattern.compile("\\bCONCAT_WS\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("GROUP_CONCAT", Pattern.compile("\\bGROUP_CONCAT\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("SUBSTRING_INDEX", Pattern.compile("\\bSUBSTRING_INDEX\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("FIND_IN_SET", Pattern.compile("\\bFIND_IN_SET\\s*\\(", Pattern.CASE_INSENSITIVE));
        
        // 特殊语法
        MYSQL_PATTERNS.put("ON_DUPLICATE_KEY", Pattern.compile("\\bON\\s+DUPLICATE\\s+KEY\\s+UPDATE\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("REPLACE_INTO", Pattern.compile("\\bREPLACE\\s+INTO\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("STRAIGHT_JOIN", Pattern.compile("\\bSTRAIGHT_JOIN\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("FORCE_INDEX", Pattern.compile("\\bFORCE\\s+INDEX\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("USE_INDEX", Pattern.compile("\\bUSE\\s+INDEX\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("IGNORE_INDEX", Pattern.compile("\\bIGNORE\\s+INDEX\\b", Pattern.CASE_INSENSITIVE));
        
        // 特殊函数
        MYSQL_PATTERNS.put("SQL_CALC_FOUND_ROWS", Pattern.compile("\\bSQL_CALC_FOUND_ROWS\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("FOUND_ROWS", Pattern.compile("\\bFOUND_ROWS\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("LAST_INSERT_ID", Pattern.compile("\\bLAST_INSERT_ID\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("CONNECTION_ID", Pattern.compile("\\bCONNECTION_ID\\s*\\(", Pattern.CASE_INSENSITIVE));
        
        // JSON函数
        MYSQL_PATTERNS.put("JSON_EXTRACT", Pattern.compile("\\bJSON_EXTRACT\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("JSON_UNQUOTE", Pattern.compile("\\bJSON_UNQUOTE\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("JSON_OBJECT", Pattern.compile("\\bJSON_OBJECT\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("JSON_ARRAY", Pattern.compile("\\bJSON_ARRAY\\s*\\(", Pattern.CASE_INSENSITIVE));
        
        // 正则表达式
        MYSQL_PATTERNS.put("REGEXP", Pattern.compile("\\bREGEXP\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("RLIKE", Pattern.compile("\\bRLIKE\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("REGEXP_REPLACE", Pattern.compile("\\bREGEXP_REPLACE\\s*\\(", Pattern.CASE_INSENSITIVE));
        
        // 数学函数
        MYSQL_PATTERNS.put("POW", Pattern.compile("\\bPOW\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("LOG", Pattern.compile("\\bLOG\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("LOG10", Pattern.compile("\\bLOG10\\s*\\(", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("RAND", Pattern.compile("\\bRAND\\s*\\(", Pattern.CASE_INSENSITIVE));
        
        // 数据类型转换
        MYSQL_PATTERNS.put("CAST_SIGNED", Pattern.compile("\\bCAST\\s*\\([^)]+\\bAS\\s+SIGNED\\b", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("CAST_UNSIGNED", Pattern.compile("\\bCAST\\s*\\([^)]+\\bAS\\s+UNSIGNED\\b", Pattern.CASE_INSENSITIVE));
        
        // 复杂模式
        MYSQL_PATTERNS.put("BACKTICKS", Pattern.compile("`[^`]+`"));
        MYSQL_PATTERNS.put("LIMIT_CLAUSE", Pattern.compile("\\bLIMIT\\s+\\d+", Pattern.CASE_INSENSITIVE));
        MYSQL_PATTERNS.put("LIMIT_OFFSET", Pattern.compile("\\bLIMIT\\s+\\d+\\s*,\\s*\\d+", Pattern.CASE_INSENSITIVE));
    }

    /**
     * 检测SQL中的所有MySQL模式
     */
    public static SqlPatternAnalysis analyzeSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return new SqlPatternAnalysis();
        }

        SqlPatternAnalysis analysis = new SqlPatternAnalysis();
        analysis.originalSql = sql;
        analysis.detectedPatterns = new HashMap<>();
        analysis.complexityScore = 0;
        analysis.recommendations = new ArrayList<>();

        // 检测各种模式
        for (Map.Entry<String, Pattern> entry : MYSQL_PATTERNS.entrySet()) {
            String patternName = entry.getKey();
            Pattern pattern = entry.getValue();
            
            if (pattern.matcher(sql).find()) {
                analysis.detectedPatterns.put(patternName, true);
                analysis.complexityScore += getPatternComplexity(patternName);
                analysis.recommendations.add(getPatternRecommendation(patternName));
            }
        }

        // 检测复杂度指标
        analysis.hasNestedQueries = sql.toLowerCase().contains("select") && 
                                   sql.toLowerCase().indexOf("select") != sql.toLowerCase().lastIndexOf("select");
        analysis.hasMultipleTables = sql.toLowerCase().contains("join") || 
                                   sql.toLowerCase().contains("from") && sql.split("(?i)\\bfrom\\b").length > 2;
        analysis.hasVariables = analysis.detectedPatterns.containsKey("USER_VARIABLE");
        analysis.hasComplexFunctions = analysis.detectedPatterns.containsKey("GROUP_CONCAT") || 
                                     analysis.detectedPatterns.containsKey("JSON_EXTRACT");

        // 评估转换难度
        analysis.conversionDifficulty = assessConversionDifficulty(analysis);
        
        // 生成转换策略建议
        analysis.conversionStrategy = generateConversionStrategy(analysis);

        return analysis;
    }

    /**
     * 获取模式复杂度分数
     */
    private static int getPatternComplexity(String patternName) {
        switch (patternName) {
            case "ROWINDEX_PATTERN":
            case "USER_VARIABLE":
            case "VARIABLE_ASSIGNMENT":
                return 10; // 变量是最复杂的
            case "GROUP_CONCAT":
            case "JSON_EXTRACT":
            case "REGEXP_REPLACE":
                return 8; // 复杂函数
            case "ON_DUPLICATE_KEY":
            case "REPLACE_INTO":
                return 7; // 特殊语法
            case "IFNULL":
            case "DATE_FORMAT":
            case "UNIX_TIMESTAMP":
                return 5; // 常见函数
            case "BACKTICKS":
            case "LIMIT_CLAUSE":
                return 3; // 简单语法
            default:
                return 4; // 默认复杂度
        }
    }

    /**
     * 获取模式转换建议
     */
    private static String getPatternRecommendation(String patternName) {
        switch (patternName) {
            case "ROWINDEX_PATTERN":
                return "使用ROW_NUMBER()窗口函数替换@rowindex变量";
            case "USER_VARIABLE":
                return "MySQL用户变量需要转换为达梦兼容语法";
            case "IFNULL":
                return "IFNULL函数转换为NVL函数";
            case "GROUP_CONCAT":
                return "GROUP_CONCAT转换为LISTAGG函数";
            case "JSON_EXTRACT":
                return "JSON_EXTRACT转换为JSON_VALUE函数";
            case "ON_DUPLICATE_KEY":
                return "ON DUPLICATE KEY UPDATE转换为ON CONFLICT语法";
            case "BACKTICKS":
                return "移除MySQL反引号标识符";
            case "LIMIT_CLAUSE":
                return "LIMIT子句转换为ROWNUM或TOP语法";
            default:
                return "需要转换为达梦兼容语法";
        }
    }

    /**
     * 评估转换难度
     */
    private static String assessConversionDifficulty(SqlPatternAnalysis analysis) {
        if (analysis.complexityScore >= 20) {
            return "VERY_HIGH";
        } else if (analysis.complexityScore >= 15) {
            return "HIGH";
        } else if (analysis.complexityScore >= 10) {
            return "MEDIUM";
        } else if (analysis.complexityScore >= 5) {
            return "LOW";
        } else {
            return "VERY_LOW";
        }
    }

    /**
     * 生成转换策略
     */
    private static String generateConversionStrategy(SqlPatternAnalysis analysis) {
        if (analysis.hasVariables) {
            return "COMPLEX_VARIABLE_CONVERSION";
        } else if (analysis.hasComplexFunctions) {
            return "FUNCTION_CONVERSION";
        } else if (analysis.hasNestedQueries) {
            return "NESTED_QUERY_CONVERSION";
        } else {
            return "BASIC_CONVERSION";
        }
    }

    /**
     * SQL模式分析结果
     */
    public static class SqlPatternAnalysis {
        public String originalSql;
        public Map<String, Boolean> detectedPatterns;
        public int complexityScore;
        public List<String> recommendations;
        public boolean hasNestedQueries;
        public boolean hasMultipleTables;
        public boolean hasVariables;
        public boolean hasComplexFunctions;
        public String conversionDifficulty;
        public String conversionStrategy;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("SQL模式分析结果:\n");
            sb.append("复杂度分数: ").append(complexityScore).append("\n");
            sb.append("转换难度: ").append(conversionDifficulty).append("\n");
            sb.append("转换策略: ").append(conversionStrategy).append("\n");
            sb.append("检测到的模式: ").append(detectedPatterns.size()).append("个\n");
            
            if (!detectedPatterns.isEmpty()) {
                sb.append("具体模式:\n");
                detectedPatterns.forEach((pattern, detected) -> {
                    if (detected) {
                        sb.append("  - ").append(pattern).append("\n");
                    }
                });
            }
            
            if (!recommendations.isEmpty()) {
                sb.append("转换建议:\n");
                recommendations.forEach(rec -> sb.append("  - ").append(rec).append("\n"));
            }
            
            return sb.toString();
        }
    }
}
