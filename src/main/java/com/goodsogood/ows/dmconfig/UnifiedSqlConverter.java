package com.goodsogood.ows.dmconfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 统一的SQL转换器
 * 整合了ComplexSqlConverter和SqlCompatibilityUtil的所有功能
 * 提供统一的SQL转换入口
 */
@Slf4j
public class UnifiedSqlConverter {
    
    /**
     * 统一的SQL转换方法
     * 处理所有类型的SQL转换需求
     * 
     * @param sql 原始SQL
     * @return 转换后的SQL
     */
    public static String convertSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }
        
        try {
            log.debug("开始SQL转换: {}", sql.substring(0, Math.min(100, sql.length())));
            
            String result = sql;
            
            // 第一阶段：预处理和清理
            result = preprocessSql(result);
            
            // 第二阶段：MySQL特有语法转换
            result = convertMysqlSyntax(result);
            
            // 第三阶段：变量和函数转换
            result = convertVariablesAndFunctions(result);
            
            // 第四阶段：后处理和格式化
            result = postprocessSql(result);
            
            log.debug("SQL转换完成: {}", result.substring(0, Math.min(100, result.length())));
            return result;
            
        } catch (Exception e) {
            log.error("SQL转换失败: {}", e.getMessage(), e);
            return sql; // 转换失败时返回原始SQL
        }
    }
    
    /**
     * 第一阶段：预处理和清理
     */
    private static String preprocessSql(String sql) {
        // 1. 基础格式清理
        sql = sql.replaceAll("\\s+", " ").trim();
        
        // 2. 移除反引号
        sql = sql.replaceAll("`([^`]+)`", "$1");
        
        // 3. 处理LIMIT语法
        sql = sql.replaceAll("\\s+LIMIT\\s+(\\d+)(?:\\s*,\\s*(\\d+))?", " LIMIT $2 OFFSET $1");
        
        return sql;
    }
    
    /**
     * 第二阶段：MySQL特有语法转换
     */
    private static String convertMysqlSyntax(String sql) {
        // 1. 首先精确移除初始化语句，避免被后续处理误转换
        sql = removeInitializationStatements(sql);
        
        // 2. 转换MySQL变量语法
        sql = convertMysqlVariables(sql);
        
        // 3. 转换MySQL特有函数
        sql = convertMysqlFunctions(sql);
        
        // 4. 转换其他MySQL语法
        sql = convertOtherMysqlSyntax(sql);
        
        return sql;
    }
    
    /**
     * 移除变量初始化语句
     */
    private static String removeInitializationStatements(String sql) {
        // 使用最安全的正则表达式处理所有可能的初始化语句
        // 支持任意别名，限制长度避免误匹配
        sql = sql.replaceAll("(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        sql = sql.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        sql = sql.replaceAll("(?i),\\s*\\(\\s*select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        
        // 处理其他数字的初始化
        sql = sql.replaceAll("(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*0\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        sql = sql.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*0\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        sql = sql.replaceAll("(?i),\\s*\\(\\s*select\\s+@rowindex\\s*:=\\s*0\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        
        // 处理正数初始化
        sql = sql.replaceAll("(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*\\d+\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        sql = sql.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*\\d+\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        sql = sql.replaceAll("(?i),\\s*\\(\\s*select\\s+@rowindex\\s*:=\\s*\\d+\\s*\\)\\s*\\w{0,20}\\b\\s*", " ");
        
        // 兜底的精确字符串替换（处理常见的固定别名）
        sql = sql.replace(",(Select @rowindex:=-1) b", "");
        sql = sql.replace(", (Select @rowindex:=-1) b", "");
        sql = sql.replace(",(SELECT @rowindex:=-1) b", "");
        sql = sql.replace(", (SELECT @rowindex:=-1) b", "");
        sql = sql.replace(",(select @rowindex:=-1) b", "");
        sql = sql.replace(", (select @rowindex:=-1) b", "");
        sql = sql.replace(",(Select @rowindex:=-1) r", "");
        sql = sql.replace(", (Select @rowindex:=-1) r", "");
        sql = sql.replace(",(Select @rowindex:=-1) init", "");
        sql = sql.replace(", (Select @rowindex:=-1) init", "");
        
        return sql;
    }
    
    /**
     * 转换MySQL变量语法
     */
    private static String convertMysqlVariables(String sql) {
        // 转换@rowindex递增为ROW_NUMBER()
        sql = sql.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", 
                           "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
        
        // 清理残留的@rowindex（不在初始化语句中的）
        if (sql.toLowerCase().contains("@rowindex")) {
            sql = sql.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
            log.warn("发现无法精确转换的@rowindex，已替换为常量1");
        }
        
        // 转换其他MySQL变量
        sql = sql.replaceAll("(?i)@\\w+\\s*:=\\s*[^,)]+", "");
        
        return sql;
    }
    
    /**
     * 转换MySQL特有函数
     */
    private static String convertMysqlFunctions(String sql) {
        // 时间函数转换
        sql = sql.replaceAll("\\bNOW\\(\\)", "SYSDATE");
        sql = sql.replaceAll("\\bCURDATE\\(\\)", "TRUNC(SYSDATE)");
        sql = sql.replaceAll("\\bCURTIME\\(\\)", "TO_CHAR(SYSDATE, 'HH24:MI:SS')");
        
        // 字符串函数转换
        sql = sql.replaceAll("\\bIFNULL\\s*\\(([^,]+),([^)]+)\\)", "NVL($1,$2)");
        sql = sql.replaceAll("\\bCONCAT\\s*\\(([^)]+)\\)", "($1)");
        
        // 数学函数转换
        sql = sql.replaceAll("\\bPOW\\s*\\(([^,]+),([^)]+)\\)", "POWER($1,$2)");
        
        return sql;
    }
    
    /**
     * 转换其他MySQL语法
     */
    private static String convertOtherMysqlSyntax(String sql) {
        // REPLACE INTO转换
        sql = sql.replaceAll("(?i)\\bREPLACE\\s+INTO\\b", "INSERT OR REPLACE INTO");
        
        // AUTO_INCREMENT转换
        sql = sql.replaceAll("(?i)\\bAUTO_INCREMENT\\b", "AUTOINCREMENT");
        
        // ENGINE转换（移除）
        sql = sql.replaceAll("(?i)\\s+ENGINE\\s*=\\s*\\w+", "");
        
        return sql;
    }
    
    /**
     * 第三阶段：变量和函数转换
     */
    private static String convertVariablesAndFunctions(String sql) {
        // 这里可以添加更多的变量和函数转换逻辑
        return sql;
    }
    
    /**
     * 第四阶段：后处理和格式化
     */
    private static String postprocessSql(String sql) {
        // 1. 修复关键字连接问题
        sql = fixKeywordConcatenation(sql);
        
        // 2. 最终格式清理
        sql = sql.replaceAll("\\s+", " ").trim();
        
        // 3. 移除多余的逗号和空格
        sql = sql.replaceAll(",\\s*,", ",");
        sql = sql.replaceAll("\\(\\s*,", "(");
        sql = sql.replaceAll(",\\s*\\)", ")");
        
        return sql;
    }
    
    /**
     * 修复关键字连接问题
     */
    private static String fixKeywordConcatenation(String sql) {
        // 处理SELECT和ROW_NUMBER连接的问题
        sql = sql.replaceAll("(?i)SELECT\\s*ROW_NUMBER", "SELECT ROW_NUMBER");
        sql = sql.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");
        
        // 处理ROW_NUMBER和OVER连接的问题
        sql = sql.replaceAll("(?i)ROW_NUMBER\\s*OVER", "ROW_NUMBER() OVER");
        sql = sql.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");
        
        // 确保ROW_NUMBER后面有括号
        sql = sql.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");
        
        // 处理OVER和括号的问题
        sql = sql.replaceAll("(?i)OVER\\s*\\(", "OVER(");
        sql = sql.replaceAll("(?i)OVER(?!\\s*\\()", "OVER(ORDER BY 1)");
        
        return sql;
    }
    
    /**
     * 检查SQL是否需要复杂转换
     * 保留此方法以便向后兼容
     */
    public static boolean needsComplexConversion(String sql) {
        if (sql == null) return false;
        
        return sql.toLowerCase().contains("@rowindex") ||
               sql.toLowerCase().contains("mysql") ||
               sql.toLowerCase().contains("limit") ||
               sql.contains("`");
    }
    
    /**
     * 简单转换方法（向后兼容）
     */
    public static String simpleConvert(String sql) {
        return convertSql(sql);
    }
    
    /**
     * 智能转换方法（向后兼容）
     */
    public static String smartConvert(String sql) {
        return convertSql(sql);
    }
}
