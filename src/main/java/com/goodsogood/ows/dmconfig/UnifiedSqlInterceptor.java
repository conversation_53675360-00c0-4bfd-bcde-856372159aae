package com.goodsogood.ows.dmconfig;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.sql.Connection;
import java.util.Properties;

/**
 * 统一的SQL拦截器
 * 整合了所有SQL转换功能，统一处理所有类型的SQL
 */
@Slf4j
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class UnifiedSqlInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        
        // 获取原始SQL
        String originalSql = (String) metaObject.getValue("delegate.boundSql.sql");
        
        if (originalSql == null || originalSql.trim().isEmpty()) {
            return invocation.proceed();
        }
        
        try {
            // 记录原始SQL（仅记录前100个字符）
            log.debug("拦截到SQL: {}", originalSql.substring(0, Math.min(100, originalSql.length())));
            
            // 使用统一转换器处理SQL
            String convertedSql = UnifiedSqlConverter.convertSql(originalSql);
            
            // 如果SQL发生了变化，记录转换信息
            if (!originalSql.equals(convertedSql)) {
                log.info("SQL已转换:");
                log.info("原始: {}", originalSql.substring(0, Math.min(200, originalSql.length())));
                log.info("转换: {}", convertedSql.substring(0, Math.min(200, convertedSql.length())));
                
                // 更新SQL
                metaObject.setValue("delegate.boundSql.sql", convertedSql);
            }
            
        } catch (Exception e) {
            log.error("SQL转换失败，使用原始SQL: {}", e.getMessage(), e);
            // 转换失败时继续使用原始SQL，不影响业务
        }
        
        return invocation.proceed();
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以通过配置文件设置拦截器属性
        log.info("统一SQL拦截器已初始化");
    }
}
