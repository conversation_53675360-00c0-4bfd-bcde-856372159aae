// 测试所有可能的空格变体
public class TestAllSpaceVariations {
    public static void main(String[] args) {
        System.out.println("=== 测试所有可能的空格变体 ===\n");
        
        // 所有可能的空格变体
        String[] spaceVariations = {
            // 基本情况
            "grades,(Select @rowindex:=-1) b",
            
            // 逗号后的空格
            "grades, (Select @rowindex:=-1) b",
            "grades,  (Select @rowindex:=-1) b",
            "grades,   (Select @rowindex:=-1) b",
            
            // 括号内的空格
            "grades,( Select @rowindex:=-1) b",
            "grades,(  Select @rowindex:=-1) b",
            "grades,(Select  @rowindex:=-1) b",
            "grades,(Select   @rowindex:=-1) b",
            
            // @rowindex周围的空格
            "grades,(Select @rowindex :=-1) b",
            "grades,(Select @rowindex  :=-1) b",
            "grades,(Select @rowindex: =-1) b",
            "grades,(Select @rowindex := -1) b",
            "grades,(Select @rowindex :=  -1) b",
            
            // 括号前的空格
            "grades,(Select @rowindex:=-1 ) b",
            "grades,(Select @rowindex:=-1  ) b",
            "grades,(Select @rowindex:=-1   ) b",
            
            // b前后的空格
            "grades,(Select @rowindex:=-1)  b",
            "grades,(Select @rowindex:=-1)   b",
            "grades,(Select @rowindex:=-1) b ",
            "grades,(Select @rowindex:=-1) b  ",
            "grades,(Select @rowindex:=-1) b   ",
            
            // 复合空格情况
            "grades,  (  Select   @rowindex  :=  -1  )   b   ",
            "grades,\t(Select\t@rowindex:=-1)\tb\t",
            "grades,\n(Select\n@rowindex:=-1)\nb\n",
            
            // 混合空白字符
            "grades, \t (Select @rowindex:=-1) b  \n",
        };
        
        System.out.println("测试 " + spaceVariations.length + " 种空格变体:\n");
        
        // 当前的修复方法
        for (int i = 0; i < spaceVariations.length; i++) {
            String original = spaceVariations[i];
            System.out.println("变体 " + (i + 1) + ": '" + original.replace("\t", "\\t").replace("\n", "\\n") + "'");
            
            // 应用当前的修复方法
            String result = original;
            result = result.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
            result = result.replaceAll("(?i),\\s*\\(Select @rowindex:=-1\\) b\\s*", " ");
            result = result.replace(",(Select @rowindex:=-1) b", "");
            result = result.replace(", (Select @rowindex:=-1) b", "");
            
            System.out.println("当前修复: '" + result + "'");
            
            boolean success = !result.contains("@rowindex");
            System.out.println("成功: " + (success ? "✅" : "❌"));
            
            if (!success) {
                System.out.println("❌ 当前方法无法处理这种变体");
            }
            
            System.out.println();
        }
        
        // 测试更强大的正则表达式
        System.out.println("=== 测试更强大的正则表达式 ===\n");
        
        // 更全面的正则表达式
        String powerfulRegex = "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*-?\\d+\\s*\\)\\s*\\w*\\s*";
        
        System.out.println("强大正则: " + powerfulRegex + "\n");
        
        for (int i = 0; i < spaceVariations.length; i++) {
            String original = spaceVariations[i];
            System.out.println("变体 " + (i + 1) + ": '" + original.replace("\t", "\\t").replace("\n", "\\n") + "'");
            
            String result = original.replaceAll(powerfulRegex, "");
            System.out.println("强大正则结果: '" + result + "'");
            
            boolean success = !result.contains("@rowindex");
            System.out.println("成功: " + (success ? "✅" : "❌"));
            System.out.println();
        }
        
        // 测试最终建议的解决方案
        System.out.println("=== 建议的最终解决方案 ===\n");
        
        for (int i = 0; i < spaceVariations.length; i++) {
            String original = spaceVariations[i];
            System.out.println("变体 " + (i + 1) + ": '" + original.replace("\t", "\\t").replace("\n", "\\n") + "'");
            
            String result = original;
            
            // 组合方法：先用强大的正则，再用精确替换
            result = result.replaceAll("(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*-?\\d+\\s*\\)\\s*\\w*\\s*", " ");
            result = result.replace(",(Select @rowindex:=-1) b", "");
            result = result.replace(", (Select @rowindex:=-1) b", "");
            
            // 清理多余空格
            result = result.replaceAll("\\s+", " ").trim();
            
            System.out.println("最终方案结果: '" + result + "'");
            
            boolean success = !result.contains("@rowindex");
            System.out.println("成功: " + (success ? "✅" : "❌"));
            System.out.println();
        }
    }
}
