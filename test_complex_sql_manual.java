// 手动测试复杂SQL转换
public class TestComplexSqlManual {
    public static void main(String[] args) {
        // 使用与控制器相同的复杂SQL
        String complexSql = "select scoreMedian1, scoreMedian2, scoreMedian3 from(" +
                "SELECT AVG(g.score1) scoreMedian1 " +
                "FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score1 AS score1 " +
                "FROM(select score1 from(select sum(NVL(tt1.total, 0)) as score1 from table1 tt1) grades) grades, " +
                "(Select @rowindex:=-1) b " +
                "ORDER BY grades.score1) g), " +
                "(select AVG(g.score2) scoreMedian2 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score2 AS score2 FROM(select score2 from(select sum(NVL(tt2.total, 0)) as score2 from table2 tt2) grades) grades, (Select @rowindex:=-1) b ORDER BY grades.score2) g), " +
                "(select AVG(g.score3) scoreMedian3 FROM(SELECT @rowindex := @rowindex + 1 AS rowindex, grades.score3 AS score3 FROM(select score3 from(select sum(NVL(tt3.total, 0)) as score3 from table3 tt3) grades) grades, (Select @rowindex:=-1) b ORDER BY grades.score3) g)";
        
        System.out.println("=== 复杂SQL手动转换测试 ===\n");
        System.out.println("原始SQL长度: " + complexSql.length());
        System.out.println("原始SQL前100字符: " + complexSql.substring(0, Math.min(100, complexSql.length())) + "...\n");
        
        // 统计原始SQL中的模式
        int rowindexCount = countOccurrences(complexSql, "@rowindex");
        int initCount = countOccurrences(complexSql, "(Select @rowindex:=-1) b");
        System.out.println("原始SQL统计:");
        System.out.println("- @rowindex出现次数: " + rowindexCount);
        System.out.println("- 初始化语句次数: " + initCount + "\n");
        
        // 手动执行转换步骤
        String result = complexSql;
        
        // 步骤1: 移除初始化语句
        System.out.println("=== 步骤1: 移除初始化语句 ===");
        String[] initPatterns = {
            // 带逗号的模式
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*",
            // 不带逗号的模式
            "(?i)\\(\\s*SELECT\\s+@rowindex\\s*:?\\s*=\\s*-\\d+\\s*\\)\\s*\\w*"
        };
        
        for (String pattern : initPatterns) {
            String before = result;
            result = result.replaceAll(pattern, "");
            if (!before.equals(result)) {
                System.out.println("应用模式: " + pattern);
                System.out.println("移除了 " + (before.length() - result.length()) + " 个字符");
            }
        }
        
        System.out.println("步骤1后长度: " + result.length());
        System.out.println("仍包含(Select @rowindex:=-1): " + result.contains("(Select @rowindex:=-1)"));
        System.out.println("步骤1后前100字符: " + result.substring(0, Math.min(100, result.length())) + "...\n");
        
        // 步骤2: 转换@rowindex递增
        System.out.println("=== 步骤2: 转换@rowindex递增 ===");
        String before2 = result;
        result = result.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", 
                                 "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
        System.out.println("转换了 " + (countOccurrences(before2, "@rowindex") - countOccurrences(result, "@rowindex")) + " 个@rowindex递增");
        System.out.println("步骤2后包含ROW_NUMBER: " + result.contains("ROW_NUMBER"));
        System.out.println("步骤2后仍包含@rowindex: " + result.contains("@rowindex") + "\n");
        
        // 步骤3: 残留清理
        if (result.contains("@rowindex")) {
            System.out.println("=== 步骤3: 残留清理 ===");
            String before3 = result;
            result = result.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
            System.out.println("残留清理完成");
            System.out.println("步骤3后包含@rowindex: " + result.contains("@rowindex"));
            
            // 检查是否产生了错误语法
            if (result.contains("(SELECT 1 := -1)") || result.contains("(SELECT 1 := 0)")) {
                System.out.println("❌ 警告: 产生了错误的语法 (SELECT 1 := 数字)");
            }
        }
        
        // 步骤4: 关键字修复
        System.out.println("\n=== 步骤4: 关键字修复 ===");
        result = result.replaceAll("(?i)SELECTROW_NUMBER", "SELECT ROW_NUMBER");
        result = result.replaceAll("(?i)ROW_NUMBEROVER", "ROW_NUMBER() OVER");
        result = result.replaceAll("(?i)ROW_NUMBER(?!\\s*\\()", "ROW_NUMBER()");
        System.out.println("关键字修复完成");
        
        // 步骤5: 最终清理
        System.out.println("\n=== 步骤5: 最终清理 ===");
        result = result.replaceAll("\\s+", " ").trim();
        System.out.println("最终清理完成");
        
        // 最终检查
        System.out.println("\n=== 最终检查 ===");
        System.out.println("最终长度: " + result.length());
        System.out.println("包含@rowindex: " + result.contains("@rowindex"));
        System.out.println("包含ROW_NUMBER: " + result.contains("ROW_NUMBER"));
        System.out.println("包含(SELECT 1 := -1): " + result.contains("(SELECT 1 := -1)"));
        System.out.println("包含SELECTROW_NUMBER: " + result.contains("SELECTROW_NUMBER"));
        
        System.out.println("\n最终结果前200字符:");
        System.out.println(result.substring(0, Math.min(200, result.length())) + "...");
        
        // 总结
        System.out.println("\n=== 转换总结 ===");
        boolean hasIssues = false;
        
        if (result.contains("(SELECT 1 := -1)")) {
            System.out.println("❌ 问题: 仍有错误的初始化残留");
            hasIssues = true;
        }
        
        if (result.contains("SELECTROW_NUMBER")) {
            System.out.println("❌ 问题: 仍有关键字连接");
            hasIssues = true;
        }
        
        if (result.contains("@rowindex")) {
            System.out.println("⚠️ 警告: 仍包含@rowindex");
        }
        
        if (!hasIssues && result.contains("ROW_NUMBER()")) {
            System.out.println("✅ 转换成功");
        } else if (hasIssues) {
            System.out.println("❌ 转换仍有问题");
        }
    }
    
    private static int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
