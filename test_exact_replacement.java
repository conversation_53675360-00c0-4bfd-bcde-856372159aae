// 测试精确字符串替换
public class TestExactReplacement {
    public static void main(String[] args) {
        System.out.println("=== 测试精确字符串替换 ===\n");
        
        // 您发现的具体问题
        String problem = "grades,(Select @rowindex:=-1) b";
        System.out.println("问题字符串: " + problem);
        
        // 精确替换测试
        String result1 = problem.replace(",(Select @rowindex:=-1) b", "");
        System.out.println("精确替换结果: '" + result1 + "'");
        
        if (result1.equals("grades")) {
            System.out.println("✅ 精确替换成功！");
        } else {
            System.out.println("❌ 精确替换失败");
        }
        
        // 测试各种变体
        System.out.println("\n=== 测试各种变体 ===");
        String[] variants = {
            "grades,(Select @rowindex:=-1) b",
            "grades, (Select @rowindex:=-1) b",  // 有空格
            "grades,(SELECT @rowindex:=-1) b",   // 全大写
            "grades, (SELECT @rowindex:=-1) b",  // 有空格+全大写
            "table,(Select @rowindex:=-1) b",
            "data,(select @rowindex:=-1) b"      // 全小写
        };
        
        for (String variant : variants) {
            System.out.println("原始: " + variant);
            
            String result = variant;
            // 应用所有可能的精确替换
            result = result.replace(",(Select @rowindex:=-1) b", "");
            result = result.replace(", (Select @rowindex:=-1) b", "");
            result = result.replace(",(SELECT @rowindex:=-1) b", "");
            result = result.replace(", (SELECT @rowindex:=-1) b", "");
            result = result.replace(",(select @rowindex:=-1) b", "");
            result = result.replace(", (select @rowindex:=-1) b", "");
            
            System.out.println("替换后: '" + result + "'");
            
            if (!result.contains("@rowindex")) {
                System.out.println("✅ 成功移除");
            } else {
                System.out.println("❌ 仍有残留");
            }
            System.out.println();
        }
        
        // 测试完整SQL
        System.out.println("=== 测试完整SQL ===");
        String fullSql = "FROM(select score1 from table1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1";
        System.out.println("完整SQL: " + fullSql);
        
        String fullResult = fullSql.replace(",(Select @rowindex:=-1) b", "");
        System.out.println("替换后: " + fullResult);
        
        if (!fullResult.contains("@rowindex")) {
            System.out.println("✅ 完整SQL替换成功");
        } else {
            System.out.println("❌ 完整SQL仍有问题");
        }
        
        // 模拟完整的转换流程
        System.out.println("\n=== 模拟完整转换流程 ===");
        String testSql = "SELECT @rowindex := @rowindex + 1 AS rowindex FROM grades,(Select @rowindex:=-1) b";
        System.out.println("测试SQL: " + testSql);
        
        // 步骤1: 精确移除初始化
        String step1 = testSql.replace(",(Select @rowindex:=-1) b", "");
        System.out.println("步骤1 - 移除初始化: " + step1);
        
        // 步骤2: 转换@rowindex递增
        String step2 = step1.replaceAll("(?i)@rowindex\\s*:?\\s*=\\s*@rowindex\\s*\\+\\s*1\\s+AS\\s+(\\w+)", 
                                       "ROW_NUMBER() OVER(ORDER BY 1) AS $1");
        System.out.println("步骤2 - 转换递增: " + step2);
        
        // 步骤3: 检查残留
        if (step2.contains("@rowindex")) {
            System.out.println("⚠️ 仍有@rowindex残留");
            String step3 = step2.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
            System.out.println("步骤3 - 残留清理: " + step3);
            
            if (step3.contains("(SELECT 1 := -1)")) {
                System.out.println("❌ 产生了错误语法");
            } else {
                System.out.println("✅ 残留清理正确");
            }
        } else {
            System.out.println("✅ 没有残留");
        }
        
        // 最终检查
        String finalResult = step2.contains("@rowindex") ? 
            step2.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1") : step2;
        
        System.out.println("\n最终结果: " + finalResult);
        
        boolean success = !finalResult.contains("@rowindex") && 
                         !finalResult.contains("(SELECT 1 := -1)") &&
                         finalResult.contains("ROW_NUMBER()");
        
        System.out.println("转换成功: " + (success ? "✅" : "❌"));
    }
}
