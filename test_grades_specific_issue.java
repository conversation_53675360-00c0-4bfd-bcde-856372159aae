// 测试具体的grades,(Select @rowindex:=-1) b问题
public class TestGradesSpecificIssue {
    public static void main(String[] args) {
        System.out.println("=== 测试具体的grades,(Select @rowindex:=-1) b问题 ===\n");
        
        // 具体的问题模式
        String specificIssue = "grades,(Select @rowindex:=-1) b";
        System.out.println("具体问题: " + specificIssue);
        
        // 测试各种正则表达式模式
        String[] patterns = {
            // 原来的模式（可能不匹配）
            "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
            
            // 新的紧密连接模式（应该匹配）
            "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
            
            // 其他可能的模式
            "(?i)\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
            "(?i),\\(Select\\s+@rowindex:=-\\d+\\)\\s*\\w*"
        };
        
        System.out.println("\n=== 测试各种正则表达式模式 ===");
        for (int i = 0; i < patterns.length; i++) {
            String pattern = patterns[i];
            String result = specificIssue.replaceAll(pattern, "");
            
            System.out.println("模式 " + (i + 1) + ": " + pattern);
            System.out.println("结果 " + (i + 1) + ": " + result);
            
            if (!result.equals(specificIssue)) {
                System.out.println("✅ 模式 " + (i + 1) + " 匹配成功，移除了初始化语句");
            } else {
                System.out.println("❌ 模式 " + (i + 1) + " 不匹配");
            }
            System.out.println();
        }
        
        // 测试更多相似的情况
        System.out.println("=== 测试相似情况 ===");
        String[] similarCases = {
            "table,(SELECT @rowindex := 0) r",
            "data,(select @rowindex:=-1) init", 
            "users, (Select @rowindex:=-1) b",  // 有空格
            "grades, (Select @rowindex:=-1) b", // 有空格
            "FROM grades,(Select @rowindex:=-1) b ORDER"
        };
        
        String bestPattern = "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*";
        String fallbackPattern = "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*";
        
        for (String testCase : similarCases) {
            System.out.println("测试: " + testCase);
            
            // 先尝试紧密连接模式
            String result1 = testCase.replaceAll(bestPattern, "");
            if (!result1.equals(testCase)) {
                System.out.println("紧密模式匹配: " + result1);
            } else {
                // 再尝试带空格模式
                String result2 = testCase.replaceAll(fallbackPattern, "");
                if (!result2.equals(testCase)) {
                    System.out.println("空格模式匹配: " + result2);
                } else {
                    System.out.println("❌ 都不匹配");
                }
            }
            System.out.println();
        }
        
        // 测试完整的转换流程
        System.out.println("=== 测试完整转换流程 ===");
        String fullSql = "FROM(select score1 from table1) grades,(Select @rowindex:=-1) b ORDER BY grades.score1";
        System.out.println("完整SQL: " + fullSql);
        
        // 步骤1: 移除初始化
        String step1 = fullSql;
        String[] allPatterns = {
            "(?i),\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
            "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*",
            "(?i)\\(\\s*Select\\s+@rowindex\\s*:=-\\d+\\s*\\)\\s*\\w*"
        };
        
        for (String pattern : allPatterns) {
            String before = step1;
            step1 = step1.replaceAll(pattern, "");
            if (!before.equals(step1)) {
                System.out.println("应用模式: " + pattern);
                System.out.println("移除后: " + step1);
                break;
            }
        }
        
        // 检查是否还有@rowindex残留
        if (step1.contains("@rowindex")) {
            System.out.println("⚠️ 仍有@rowindex残留");
            
            // 应用残留清理
            String step2 = step1.replaceAll("(?i)@rowindex(?!\\s*:?\\s*=\\s*-?\\d)", "1");
            System.out.println("残留清理: " + step2);
            
            if (step2.contains("(SELECT 1 := -1)")) {
                System.out.println("❌ 产生了错误语法");
            } else {
                System.out.println("✅ 残留清理正确");
            }
        } else {
            System.out.println("✅ 初始化完全移除");
        }
        
        // 验证修复效果
        System.out.println("\n=== 验证修复效果 ===");
        if (!step1.contains("(Select @rowindex:=-1)") && !step1.contains("(SELECT 1 := -1)")) {
            System.out.println("✅ 修复成功：没有错误的初始化语句");
        } else {
            System.out.println("❌ 修复失败：仍有问题");
        }
    }
}
