// 测试多空格问题
public class TestMultipleSpacesIssue {
    public static void main(String[] args) {
        System.out.println("=== 测试多空格问题 ===\n");
        
        // 从实际的testComplexSql方法中提取的问题模式
        String actualPattern = "grades,(Select @rowindex:=-1) b  ORDER BY grades.score1";
        System.out.println("实际问题模式: " + actualPattern);
        System.out.println("注意: 'b' 后面有两个空格");
        
        // 分析字符
        System.out.println("\n字符分析:");
        for (int i = 0; i < actualPattern.length(); i++) {
            char c = actualPattern.charAt(i);
            if (c == ' ') {
                System.out.println("位置 " + i + ": [空格]");
            } else if (i > 30) { // 只显示关键部分
                System.out.println("位置 " + i + ": '" + c + "'");
            }
        }
        
        // 测试各种替换方法
        System.out.println("\n=== 测试各种替换方法 ===");
        
        // 方法1: 简单字符串替换
        String method1 = actualPattern.replace(",(Select @rowindex:=-1) b", "");
        System.out.println("方法1 - 简单替换: " + method1);
        System.out.println("成功: " + (!method1.contains("@rowindex")));
        
        // 方法2: 处理多空格的正则表达式
        String method2 = actualPattern.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
        System.out.println("方法2 - 正则多空格: " + method2);
        System.out.println("成功: " + (!method2.contains("@rowindex")));
        
        // 方法3: 更宽松的正则表达式
        String method3 = actualPattern.replaceAll("(?i),\\s*\\(Select @rowindex:=-1\\) b\\s*", " ");
        System.out.println("方法3 - 宽松正则: " + method3);
        System.out.println("成功: " + (!method3.contains("@rowindex")));
        
        // 测试更多变体
        System.out.println("\n=== 测试更多空格变体 ===");
        String[] variants = {
            "grades,(Select @rowindex:=-1) b ORDER",      // 一个空格
            "grades,(Select @rowindex:=-1) b  ORDER",     // 两个空格
            "grades,(Select @rowindex:=-1) b   ORDER",    // 三个空格
            "grades,(Select @rowindex:=-1) b\tORDER",     // 制表符
            "grades,(Select @rowindex:=-1) b\nORDER",     // 换行符
            "grades, (Select @rowindex:=-1) b  ORDER",    // 逗号后有空格
        };
        
        for (String variant : variants) {
            System.out.println("测试: " + variant.replace("\t", "\\t").replace("\n", "\\n"));
            
            // 应用我们的修复方法
            String result = variant;
            result = result.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
            result = result.replaceAll("(?i),\\s*\\(Select @rowindex:=-1\\) b\\s*", " ");
            result = result.replace(",(Select @rowindex:=-1) b", "");
            result = result.replace(", (Select @rowindex:=-1) b", "");
            
            System.out.println("结果: " + result);
            System.out.println("成功: " + (!result.contains("@rowindex")) + "\n");
        }
        
        // 测试完整的SQL片段
        System.out.println("=== 测试完整SQL片段 ===");
        String sqlFragment = "FROM (select score1 from table) grades,(Select @rowindex:=-1) b  ORDER BY grades.score1) AS g";
        System.out.println("SQL片段: " + sqlFragment);
        
        // 应用修复
        String fixed = sqlFragment;
        fixed = fixed.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
        fixed = fixed.replaceAll("(?i),\\s*\\(Select @rowindex:=-1\\) b\\s*", " ");
        
        System.out.println("修复后: " + fixed);
        
        if (!fixed.contains("@rowindex")) {
            System.out.println("✅ SQL片段修复成功");
        } else {
            System.out.println("❌ SQL片段仍有问题");
        }
        
        // 验证不会误匹配正常内容
        System.out.println("\n=== 验证不会误匹配 ===");
        String normalSql = "SELECT * FROM grades WHERE score > 0 ORDER BY score";
        String normalResult = normalSql.replaceAll("(?i),\\(Select @rowindex:=-1\\) b\\s+", " ");
        
        System.out.println("正常SQL: " + normalSql);
        System.out.println("处理后: " + normalResult);
        System.out.println("未被误改: " + normalSql.equals(normalResult));
    }
}
