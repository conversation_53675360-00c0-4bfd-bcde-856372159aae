// 测试正常字段名不被误转换
public class TestNormalFieldNames {
    public static void main(String[] args) {
        System.out.println("=== 测试正常字段名不被误转换 ===\n");
        
        // 您提到的具体情况
        String yourCase = "SELECT deptb AS b FROM table";
        System.out.println("您的SQL: " + yourCase);
        
        // 测试我们的正则表达式是否会误匹配
        String[] ourPatterns = {
            "(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*",
            "(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*",
            "(?i),\\s*\\(\\s*select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*"
        };
        
        System.out.println("\n=== 测试我们的正则表达式 ===");
        for (int i = 0; i < ourPatterns.length; i++) {
            String pattern = ourPatterns[i];
            System.out.println("模式 " + (i + 1) + ": " + pattern);
            
            String result = yourCase.replaceAll(pattern, " ");
            System.out.println("结果: " + result);
            
            if (result.equals(yourCase)) {
                System.out.println("✅ 安全: 不会被这个模式匹配");
            } else {
                System.out.println("❌ 危险: 被这个模式误匹配了");
            }
            System.out.println();
        }
        
        // 测试更多正常的字段名情况
        System.out.println("=== 测试更多正常字段名 ===");
        String[] normalCases = {
            "SELECT deptb AS b FROM table",
            "SELECT deptb as b FROM table", 
            "SELECT dept_b AS b FROM table",
            "SELECT column1, deptb AS b, column2 FROM table",
            "SELECT t.deptb AS b FROM table t",
            "SELECT name AS b FROM users",
            "SELECT COUNT(*) AS b FROM items",
            "SELECT * FROM (SELECT id, name FROM users) b",
            "SELECT * FROM (SELECT COUNT(*) FROM orders) AS b"
        };
        
        for (String testCase : normalCases) {
            System.out.println("测试: " + testCase);
            
            // 应用所有我们的转换模式
            String result = testCase;
            
            // 应用我们的正则表达式
            result = result.replaceAll("(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*", " ");
            result = result.replaceAll("(?i),\\s*\\(\\s*SELECT\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*", " ");
            result = result.replaceAll("(?i),\\s*\\(\\s*select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*", " ");
            
            // 应用精确字符串替换
            result = result.replace(",(Select @rowindex:=-1) b", "");
            result = result.replace(", (Select @rowindex:=-1) b", "");
            result = result.replace(",(SELECT @rowindex:=-1) b", "");
            result = result.replace(", (SELECT @rowindex:=-1) b", "");
            
            System.out.println("结果: " + result);
            
            if (result.equals(testCase)) {
                System.out.println("✅ 安全: 未被修改");
            } else {
                System.out.println("❌ 被修改了");
                
                // 检查是否丢失了重要部分
                if (testCase.contains(" AS b") && !result.contains(" AS b")) {
                    System.out.println("   - 丢失了 'AS b' 部分");
                }
                if (testCase.contains(" as b") && !result.contains(" as b")) {
                    System.out.println("   - 丢失了 'as b' 部分");
                }
            }
            System.out.println();
        }
        
        // 测试边界情况：包含我们的模式但在不同上下文中
        System.out.println("=== 测试边界情况 ===");
        String[] edgeCases = {
            // 字符串中包含我们的模式
            "SELECT '(Select @rowindex:=-1) b' AS test_string FROM table",
            "SELECT data FROM table WHERE description LIKE '%Select @rowindex:=-1%'",
            
            // 注释中包含我们的模式
            "SELECT data FROM table -- (Select @rowindex:=-1) b",
            "SELECT data FROM table /* (Select @rowindex:=-1) b */ WHERE id = 1",
            
            // 正常的子查询别名为b
            "SELECT * FROM (SELECT id FROM users WHERE active = 1) b",
            "SELECT b.id FROM (SELECT id FROM users) b"
        };
        
        for (String edgeCase : edgeCases) {
            System.out.println("边界测试: " + edgeCase);
            
            String result = edgeCase;
            result = result.replaceAll("(?i),\\s*\\(\\s*Select\\s+@rowindex\\s*:=\\s*-1\\s*\\)\\s*b\\s*", " ");
            result = result.replace(",(Select @rowindex:=-1) b", "");
            
            System.out.println("结果: " + result);
            
            if (result.equals(edgeCase)) {
                System.out.println("✅ 安全: 边界情况处理正确");
            } else {
                System.out.println("⚠️ 被修改: 需要检查是否合理");
            }
            System.out.println();
        }
        
        // 总结
        System.out.println("=== 总结 ===");
        System.out.println("我们的正则表达式要求:");
        System.out.println("1. 必须以逗号开头: ,");
        System.out.println("2. 必须有括号: (");
        System.out.println("3. 必须有Select关键字");
        System.out.println("4. 必须有@rowindex:=-1模式");
        System.out.println("5. 必须以) b结尾");
        System.out.println();
        System.out.println("因此 'SELECT deptb AS b' 这种情况应该是安全的，");
        System.out.println("因为它不满足上述任何条件。");
    }
}
